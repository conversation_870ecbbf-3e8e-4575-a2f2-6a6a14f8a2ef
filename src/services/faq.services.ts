// services/faq.service.ts
import request from "./api";
import { ApiResponse } from "./api";

// ---------- Types ----------
export interface Faq {
  _id?: string;
  question: string;
  answer: string;
}

// ---------- Functions ----------
export const getFaqs = async (): Promise<ApiResponse<Faq[]>> => {
  return request<ApiResponse<Faq[]>>("/faqs", "GET", undefined, undefined);
};

export const getFaqById = async (id: string): Promise<ApiResponse<Faq>> => {
  return request<ApiResponse<Faq>>(`/faqs/${id}`, "GET", undefined, undefined);
};

export const createFaq = async (
  faq: Faq,
  token: string,
): Promise<ApiResponse<Faq>> => {
  return request<ApiResponse<Faq>>("/faqs", "POST", faq, token);
};

export const updateFaq = async (
  id: string,
  faq: Faq,
  token: string,
): Promise<ApiResponse<Faq>> => {
  return request<ApiResponse<Faq>>(`/faqs/${id}`, "PUT", faq, token);
};

export const markFaqAsHelpful = async (
  id: string,
): Promise<ApiResponse<null>> => {
  return request<ApiResponse<null>>(
    `/faqs/${id}/helpful`,
    "PATCH",
    undefined,
    undefined,
  );
};

export const markFaqAsUnhelpful = async (
  id: string,
): Promise<ApiResponse<null>> => {
  return request<ApiResponse<null>>(
    `/faqs/${id}/not-helpful`,
    "PATCH",
    undefined,
    undefined,
  );
};

export const deleteFaq = async (
  id: string,
  token: string,
): Promise<ApiResponse<null>> => {
  return request<ApiResponse<null>>(`/faqs/${id}`, "DELETE", undefined, token);
};
