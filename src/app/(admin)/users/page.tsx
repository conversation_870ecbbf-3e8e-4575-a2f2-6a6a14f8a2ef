"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  SearchIcon,
  FilterIcon,
  XIcon,
  PlusIcon,
  EyeIcon,
  MoreVertical,
  KeyIcon,
  UserXIcon,
  Loader2,
} from "lucide-react";
import { format } from "date-fns";
import {
  User,
  PaginatedResponse,
  getUsers,
  searchUsers,
  activateUsers,
  deactivateUsers,
  createStaffMember,
  createNgoAccount,
} from "@/services/users.services";
import { getRoles, Role } from "@/services/role.services";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";

// Map for converting backend status to UI status
const statusMap: Record<string, string> = {
  active: "Active",
  inactive: "Inactive",
  pending: "Pending",
};

const statusOptions = ["Active", "Inactive", "Pending"];

export default function UsersPage() {
  const router = useRouter();
  const token = localStorage.getItem("accessToken") || "";
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [roles, setRoles] = useState<Role[]>([]);
  const [filters, setFilters] = useState({
    role: [] as string[],
    status: [] as string[],
  });
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [newUserData, setNewUserData] = useState({
    fullname: "",
    email: "",
    role: "user",
    ngoId: "",
  });

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const response = await getRoles(token);
        if (response.status === "success") {
          setRoles(response.data);
        }
      } catch (error) {
        console.error("Failed to fetch roles:", error);
      }
    };
    fetchRoles();
  }, [token]);

  const toggleFilter = (filterType: keyof typeof filters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      role: [],
      status: [],
    });
  };

  useEffect(() => {
    fetchUsers();
  }, [filters, pagination.page, pagination.limit]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm.trim()) {
        handleSearch();
      } else {
        fetchUsers();
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const roleParam =
        filters.role.length > 0 ? filters.role.join(",") : undefined;
      const statusParam =
        filters.status.length > 0
          ? filters.status.map((s) => s.toLowerCase()).join(",")
          : undefined;

      const response = await getUsers(
        {
          page: pagination.page,
          limit: pagination.limit,
          role: roleParam,
          status: statusParam,
        },
        token,
      );

      if (response.status === "success" && response.data) {
        setUsers(
          Array.isArray(response.data) ? response.data : [response.data],
        );

        const paginatedResponse =
          response as unknown as PaginatedResponse<User>;
        if (paginatedResponse.pagination) {
          setPagination({
            page: paginatedResponse.pagination.page,
            limit: paginatedResponse.pagination.limit,
            total: paginatedResponse.pagination.total,
          });
        }
      } else {
        throw new Error("Failed to fetch users");
      }
    } catch (err) {
      console.error("Error fetching users:", err);
      setError("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) return;

    try {
      setLoading(true);
      const response = await searchUsers(searchTerm, token);

      if (response.status === "success" && response.data) {
        setUsers(
          Array.isArray(response.data) ? response.data : [response.data],
        );
      } else {
        setError("Search failed");
      }
    } catch (err) {
      console.error("Error searching users:", err);
      setError("Search failed");
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (
    action: string,
    userId: string,
    currentStatus: string,
  ) => {
    setOpenMenuId(null);

    try {
      setLoading(true);

      if (action === "Activate" || action === "Deactivate") {
        if (currentStatus === "Active") {
          await deactivateUsers([userId], token);
        } else {
          await activateUsers([userId], token);
        }
        await fetchUsers();
      } else if (action === "View") {
        // Navigate to user details page
        router.push(`/users/${userId}`);
      } else if (action === "ResetPassword") {
        console.log("Reset password for user:", userId);
      }
    } catch (err) {
      console.error(`Error performing ${action} action:`, err);
      setError(`Failed to ${action.toLowerCase()} user`);
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async () => {
    if (!newUserData.fullname || !newUserData.email) {
      setError("Name and email are required");
      return;
    }

    try {
      setLoading(true);
      let response;

      if (newUserData.role === "ngo") {
        if (!newUserData.ngoId) {
          setError("NGO ID is required for NGO accounts");
          setLoading(false);
          return;
        }
        response = await createNgoAccount(
          {
            fullname: newUserData.fullname,
            email: newUserData.email,
            password: "TemporaryPassword123!",
            ngoId: newUserData.ngoId,
          },
          token,
        );
      } else {
        response = await createStaffMember(
          {
            fullname: newUserData.fullname,
            email: newUserData.email,
            role: newUserData.role,
            ngoId: newUserData.ngoId || undefined,
          },
          token,
        );
      }

      if (response.status === "success") {
        setNewUserData({
          fullname: "",
          email: "",
          role: "user",
          ngoId: "",
        });
        setShowAddUserModal(false);
        await fetchUsers();
      } else {
        setError(response.message || "Failed to create user");
      }
    } catch (err) {
      console.error("Error creating user:", err);
      setError("Failed to create user");
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatRole = (role: string | { name: string }) => {
    if (!role) return "";
    const roleName = typeof role === "object" ? role.name : role;
    return roleName
      .toLowerCase()
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy");
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    const startPage = Math.max(
      1,
      pagination.page - Math.floor(maxVisiblePages / 2),
    );
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="mt-6 flex items-center justify-between">
        <div className="text-sm text-gray-700 dark:text-gray-300">
          Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
          {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
          {pagination.total} results
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={pagination.page === 1}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            Previous
          </button>
          {pages.map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`rounded-md px-3 py-2 text-sm font-medium ${
                page === pagination.page
                  ? "bg-blue-600 text-white"
                  : "border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
              }`}
            >
              {page}
            </button>
          ))}
          <button
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={pagination.page === totalPages}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            Next
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      {error && (
        <div className="mb-6 rounded-lg bg-red-100 p-4 text-red-800">
          <div className="flex items-center gap-2">
            <span>Error: {error}</span>
            <button
              onClick={fetchUsers}
              className="ml-auto text-sm underline hover:no-underline"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className="mb-8 flex flex-col justify-between gap-6 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Users
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage and view all registered users ({pagination.total} total)
          </p>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search users..."
              className="w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <FilterIcon className="h-4 w-4" />
              Filters
              {(filters.role.length > 0 || filters.status.length > 0) && (
                <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {filters.role.length + filters.status.length}
                </span>
              )}
            </button>
            <button
              onClick={() => setShowAddUserModal(true)}
              className="flex items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <PlusIcon className="h-4 w-4" />
              Add User
            </button>
          </div>
        </div>
      </div>

      {showFilters && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <div className="flex gap-2">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <XIcon className="h-4 w-4" />
                Clear all
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              >
                <XIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Role
              </h4>
              <div className="space-y-2">
                {roles.map((role) => (
                  <div key={role._id} className="flex items-center">
                    <input
                      id={`role-${role._id}`}
                      type="checkbox"
                      checked={filters.role.includes(role._id)}
                      onChange={() => toggleFilter("role", role._id)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`role-${role._id}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {role.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </h4>
              <div className="space-y-2">
                {statusOptions.map((status) => (
                  <div key={status} className="flex items-center">
                    <input
                      id={`status-${status}`}
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={() => toggleFilter("status", status)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`status-${status}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {status}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {(filters.role.length > 0 || filters.status.length > 0) && (
        <div className="mb-4 flex flex-wrap gap-2">
          {filters.role.map((roleId) => {
            const role = roles.find((r) => r._id === roleId);
            return (
              <span
                key={`role-${roleId}`}
                className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                Role: {role ? role.name : "Unknown"}
                <button
                  onClick={() => toggleFilter("role", roleId)}
                  className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-800 hover:bg-blue-200 dark:text-blue-200 dark:hover:bg-blue-800"
                >
                  <XIcon className="h-3 w-3" />
                </button>
              </span>
            );
          })}
          {filters.status.map((status) => (
            <span
              key={`status-${status}`}
              className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              Status: {status}
              <button
                onClick={() => toggleFilter("status", status)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-800 hover:bg-green-200 dark:text-green-200 dark:hover:bg-green-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm dark:border-gray-700">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                Loading users...
              </span>
            </div>
          ) : (
            <Table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <TableHeader>
                <TableRow className="bg-gray-50 dark:bg-gray-800">
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    User
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Email
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Role
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Status
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Date Joined
                  </TableHead>
                  <TableHead className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                {users.map((user) => {
                  const displayStatus = statusMap[user.status] || user.status;
                  const displayRole = formatRole(user.role);

                  return (
                    <TableRow
                      key={user._id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    >
                      <TableCell className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 dark:text-white">
                        {user.fullname}
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4 text-gray-600 dark:text-gray-400">
                        {user.email}
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4">
                        <span className="inline-flex items-center rounded-full border border-gray-300 bg-gray-50 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300">
                          {displayRole}
                        </span>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4">
                        <span
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(
                            displayStatus,
                          )}`}
                        >
                          {displayStatus}
                        </span>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4 text-gray-600 dark:text-gray-400">
                        {formatDate(user.createdAt)}
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4 text-right">
                        <div className="relative inline-block text-left">
                          <div>
                            <button
                              type="button"
                              onClick={() =>
                                setOpenMenuId(
                                  openMenuId === user._id ? null : user._id,
                                )
                              }
                              className="inline-flex items-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600 focus:outline-none dark:hover:bg-gray-700 dark:hover:text-gray-300"
                              id={`menu-button-${user._id}`}
                              aria-expanded={openMenuId === user._id}
                              aria-haspopup="true"
                            >
                              <MoreVertical className="h-5 w-5" />
                            </button>
                          </div>

                          {openMenuId === user._id && (
                            <div
                              className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800"
                              role="menu"
                              aria-orientation="vertical"
                              aria-labelledby={`menu-button-${user._id}`}
                            >
                              <div className="py-1" role="none">
                                <button
                                  onClick={() =>
                                    handleAction(
                                      "View",
                                      user._id,
                                      displayStatus,
                                    )
                                  }
                                  className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                  role="menuitem"
                                >
                                  <EyeIcon className="mr-3 h-4 w-4" />
                                  View Details
                                </button>
                                {/* <button
                                  onClick={() =>
                                    handleAction(
                                      "ResetPassword",
                                      user._id,
                                      displayStatus,
                                    )
                                  }
                                  className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                  role="menuitem"
                                >
                                  <KeyIcon className="mr-3 h-4 w-4" />
                                  Reset Password
                                </button> */}
                                <button
                                  onClick={() =>
                                    handleAction(
                                      displayStatus === "Active"
                                        ? "Deactivate"
                                        : "Activate",
                                      user._id,
                                      displayStatus,
                                    )
                                  }
                                  className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:text-red-500 dark:hover:bg-gray-700"
                                  role="menuitem"
                                >
                                  <UserXIcon className="mr-3 h-4 w-4" />
                                  {displayStatus === "Active"
                                    ? "Deactivate"
                                    : "Activate"}
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
                {users.length === 0 && !loading && (
                  <TableRow>
                    <TableCell
                      colSpan={6}
                      className="py-12 text-center text-gray-500"
                    >
                      <div className="flex flex-col items-center justify-center gap-2">
                        <SearchIcon className="h-8 w-8 text-gray-400" />
                        <p className="text-lg font-medium">No users found</p>
                        <p className="text-sm text-gray-500">
                          Try adjusting your search or filters
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </div>

      {renderPagination()}

      {showAddUserModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="w-full max-w-md rounded-lg bg-white shadow-xl dark:bg-gray-800">
            <div className="p-6">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Add New User
                </h3>
                <button
                  onClick={() => setShowAddUserModal(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <XIcon className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="fullname"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="fullname"
                    value={newUserData.fullname}
                    onChange={(e) =>
                      setNewUserData({
                        ...newUserData,
                        fullname: e.target.value,
                      })
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label
                    htmlFor="role"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Role
                  </label>
                  <select
                    id="role"
                    value={newUserData.role}
                    onChange={(e) =>
                      setNewUserData({ ...newUserData, role: e.target.value })
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  >
                    {roles.map((role) => (
                      <option key={role._id} value={role._id}>
                        {role.name}
                      </option>
                    ))}
                  </select>
                </div>
                {newUserData.role === "ngo" && (
                  <div>
                    <label
                      htmlFor="ngoId"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      NGO ID
                    </label>
                    <input
                      type="text"
                      id="ngoId"
                      value={newUserData.ngoId}
                      onChange={(e) =>
                        setNewUserData({
                          ...newUserData,
                          ngoId: e.target.value,
                        })
                      }
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                )}
              </div>

              <div className="mt-6 flex justify-end gap-3">
                <button
                  onClick={() => setShowAddUserModal(false)}
                  className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddUser}
                  className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Add User
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
