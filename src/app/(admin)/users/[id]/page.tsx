"use client";

import Image from "next/image";
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  ArrowLeft,
  User,
  Mail,
  Calendar,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  Building2,
  Phone,
  MapPin,
  Globe,
  Award,
  Star,
} from "lucide-react";
import { User as UserType } from "@/services/users.services";

// Default Avatar Component
const DefaultAvatar = ({
  name,
  size = "h-32 w-32",
}: {
  name: string;
  size?: string;
}) => {
  const initials = name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase();

  return (
    <div
      className={`${size} flex items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-blue-600 text-2xl font-bold text-white shadow-2xl`}
    >
      {initials}
    </div>
  );
};

// Loading Skeleton Component
const UserDetailsSkeleton = () => (
  <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
    <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      {/* Header Skeleton */}
      <div className="mb-8">
        <div className="h-8 w-48 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
        <div className="mt-2 h-4 w-64 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
      </div>

      {/* Main Card Skeleton */}
      <div className="overflow-hidden rounded-3xl bg-white/80 shadow-2xl backdrop-blur-xl dark:bg-gray-800/80">
        <div className="px-8 py-8">
          <div className="flex flex-col items-center space-y-6 lg:flex-row lg:space-x-8 lg:space-y-0">
            {/* Avatar Skeleton */}
            <div className="h-32 w-32 animate-pulse rounded-full bg-gray-300 dark:bg-gray-600"></div>

            {/* Info Skeleton */}
            <div className="flex-1 space-y-4">
              <div className="h-8 w-48 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
              <div className="h-4 w-32 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
              <div className="h-4 w-40 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default function UserDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;

  const [user, setUser] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUserDetails();
  }, [userId]);

  const fetchUserDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Sample user data
      const sampleUser: UserType = {
        _id: userId,
        fullname: "Dr. Sarah Mhango",
        email: "<EMAIL>",
        status: "active",
        emailVerified: true,
        twoFA: true,
        ngoId: "ngo_12345",
        createdAt: "2023-01-15T08:30:00Z",
        role: {
          _id: "role_001",
          name: "Super Admin",
          description: "Full system access with all administrative privileges",
          permissions: [
            "user_management",
            "organization_management",
            "system_settings",
            "financial_oversight",
            "report_generation",
            "audit_logs",
            "data_export",
            "user_roles",
            "content_management",
            "security_settings",
          ],
          createdAt: "2023-01-01T00:00:00Z",
          updatedAt: "2023-01-01T00:00:00Z",
          __v: 0,
        },
      };

      setUser(sampleUser);
    } catch (err) {
      setError("An error occurred while fetching user details");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "inactive":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return <UserDetailsSkeleton />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
            <h3 className="text-lg font-medium">Error</h3>
            <p className="mt-1">{error}</p>
            <button
              onClick={() => router.back()}
              className="mt-4 inline-flex items-center gap-2 rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700"
            >
              <ArrowLeft className="h-4 w-4" />
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="rounded-lg bg-yellow-100 p-4 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
            <h3 className="text-lg font-medium">User Not Found</h3>
            <p className="mt-1">The requested user could not be found.</p>
            <button
              onClick={() => router.back()}
              className="mt-4 inline-flex items-center gap-2 rounded-md bg-yellow-600 px-4 py-2 text-sm font-medium text-white hover:bg-yellow-700"
            >
              <ArrowLeft className="h-4 w-4" />
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="animate-fade-in mb-8">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.back()}
              className="flex items-center gap-2 rounded-lg bg-white/80 px-4 py-2 text-gray-700 shadow-md backdrop-blur-xl transition-all hover:bg-white hover:shadow-lg dark:bg-gray-800/80 dark:text-gray-300 dark:hover:bg-gray-800"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Users
            </button>
          </div>
          <h1 className="mt-4 bg-gradient-to-r from-blue-600 to-blue-600 bg-clip-text text-4xl font-bold text-transparent">
            User Details
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Detailed information about {user.fullname}
          </p>
        </div>

        {/* Main Profile Card */}
        <div className="hover:shadow-3xl animate-fade-in-up relative overflow-hidden rounded-3xl bg-white/80 shadow-2xl backdrop-blur-xl transition-all duration-500 dark:bg-gray-800/80">
          {/* Profile Info Section */}
          <div className="relative px-8 py-8">
            <div className="flex flex-col items-center space-y-6 lg:flex-row lg:space-x-8 lg:space-y-0">
              {/* Profile Picture */}
              <div className="relative">
                <DefaultAvatar name={user.fullname} />
              </div>

              {/* Basic Info */}
              <div className="flex-1 text-center lg:text-left">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {user.fullname}
                </h2>
                <p className="mt-2 text-lg text-gray-600 dark:text-gray-400">
                  {user.role?.name || "User"}
                </p>

                {/* Status Badge */}
                <div className="mt-4 flex justify-center lg:justify-start">
                  <span
                    className={`inline-flex items-center gap-1 rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(user.status)}`}
                  >
                    {getStatusIcon(user.status)}
                    {user.status?.charAt(0).toUpperCase() +
                      user.status?.slice(1)}
                  </span>
                </div>

                {/* Contact Info */}
                <div className="mt-6 space-y-3">
                  <div className="flex items-center justify-center gap-3 lg:justify-start">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-700 dark:text-gray-300">
                      {user.email}
                    </span>
                  </div>

                  <div className="flex items-center justify-center gap-3 lg:justify-start">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Joined {formatDate(user.createdAt)}
                    </span>
                  </div>

                  <div className="flex items-center justify-center gap-3 lg:justify-start">
                    <Shield className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-700 dark:text-gray-300">
                      {user.role?.name || "No role assigned"}
                    </span>
                  </div>

                  {user.ngoId && (
                    <div className="flex items-center justify-center gap-3 lg:justify-start">
                      <Building2 className="h-5 w-5 text-gray-400" />
                      <span className="text-gray-700 dark:text-gray-300">
                        Organization Member
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information Cards */}
        <div className="mt-8 grid gap-6 lg:grid-cols-2">
          {/* Account Information */}
          <div className="animate-fade-in-up rounded-2xl bg-white/80 p-6 shadow-xl backdrop-blur-xl transition-all duration-500 hover:shadow-2xl dark:bg-gray-800/80">
            <h3 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
              Account Information
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  User ID
                </span>
                <span className="font-mono text-sm text-gray-900 dark:text-white">
                  {user._id}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Email Verified
                </span>
                <span
                  className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${
                    user.emailVerified
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                  }`}
                >
                  {user.emailVerified ? (
                    <>
                      <CheckCircle className="h-3 w-3" />
                      Verified
                    </>
                  ) : (
                    <>
                      <XCircle className="h-3 w-3" />
                      Not Verified
                    </>
                  )}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Two-Factor Auth
                </span>
                <span
                  className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${
                    user.twoFA
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                      : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                  }`}
                >
                  {user.twoFA ? (
                    <>
                      <Shield className="h-3 w-3" />
                      Enabled
                    </>
                  ) : (
                    <>
                      <Shield className="h-3 w-3" />
                      Disabled
                    </>
                  )}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Account Created
                </span>
                <span className="text-gray-900 dark:text-white">
                  {formatDate(user.createdAt)}
                </span>
              </div>
            </div>
          </div>

          {/* Role & Permissions */}
          <div className="animate-fade-in-up rounded-2xl bg-white/80 p-6 shadow-xl backdrop-blur-xl transition-all duration-500 hover:shadow-2xl dark:bg-gray-800/80">
            <h3 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
              Role & Permissions
            </h3>
            <div className="space-y-4">
              <div>
                <span className="text-gray-600 dark:text-gray-400">Role</span>
                <div className="mt-1">
                  <span className="inline-flex items-center gap-2 rounded-lg bg-blue-100 px-3 py-2 text-sm font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                    <Shield className="h-4 w-4" />
                    {user.role?.name || "No role assigned"}
                  </span>
                </div>
              </div>

              {user.role?.description && (
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Description
                  </span>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {user.role.description}
                  </p>
                </div>
              )}

              {user.role?.permissions && user.role.permissions.length > 0 && (
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Permissions
                  </span>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {user.role.permissions
                      .slice(0, 6)
                      .map((permission, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                        >
                          {permission}
                        </span>
                      ))}
                    {user.role.permissions.length > 6 && (
                      <span className="inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                        +{user.role.permissions.length - 6} more
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Organization Information (if applicable) */}
        {user.ngoId && (
          <div className="mt-6">
            <div className="animate-fade-in-up rounded-2xl bg-white/80 p-6 shadow-xl backdrop-blur-xl transition-all duration-500 hover:shadow-2xl dark:bg-gray-800/80">
              <h3 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
                Organization Information
              </h3>
              <div className="flex items-center gap-3">
                <Building2 className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-gray-900 dark:text-white">
                    This user is associated with an organization
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Organization ID: {user.ngoId}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
