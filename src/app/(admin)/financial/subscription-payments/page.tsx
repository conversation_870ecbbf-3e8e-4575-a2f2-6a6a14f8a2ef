"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Search,
  Filter,
  Download,
  XIcon,
} from "lucide-react";

import {
  getAllSubscriptions,
  approveSubscription,
  rejectSubscription,
  Subscription,
} from "@/services/subscription.services";
import ExportModal from "@/components/ExportModal";
import {
  downloadCSV,
  downloadJSON,
  formatCurrency,
  formatDate,
  formatStatus,
  ExportColumn,
} from "@/utils/exportUtils";

export default function SubscriptionPaymentsPage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedSubscription, setSelectedSubscription] =
    useState<Subscription | null>(null);
  const [rejectReason, setRejectReason] = useState("");

  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [filters, setFilters] = useState({
    status: [] as string[],
    paymentMethod: [] as string[],
  });

  useEffect(() => {
    const fetchSubscriptions = async () => {
      try {
        const token = localStorage.getItem("accessToken");
        if (token) {
          const response = await getAllSubscriptions(token);
          if (response.status === "success") {
            setSubscriptions(response.data?.subscriptions || []);
          } else {
            setError(response.message);
          }
        } else {
          setError("Authentication token not found.");
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptions();
  }, []);

  const onViewCertificate = (certificateId: string) => {
    // Handle certificate view logic, e.g., navigate to a certificate page
    console.log("Viewing certificate:", certificateId);
  };

  const handleApprove = async (subscriptionId: string) => {
    const token = localStorage.getItem("accessToken");
    if (!token) {
      setError("Authentication token not found.");
      return;
    }

    setProcessingId(subscriptionId);
    try {
      const response = await approveSubscription(subscriptionId, token);
      if (response.status === "success") {
        // Refresh the subscriptions list
        const updatedResponse = await getAllSubscriptions(token);
        if (updatedResponse.status === "success") {
          setSubscriptions(updatedResponse.data?.subscriptions || []);
        }
        setError(null);
      } else {
        setError(response.message || "Failed to approve subscription");
      }
    } catch (err: any) {
      setError(err.message || "Error approving subscription");
    } finally {
      setProcessingId(null);
    }
  };

  const handleRejectClick = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setShowRejectModal(true);
    setRejectReason("");
  };

  const handleRejectSubmit = async () => {
    if (!selectedSubscription || !rejectReason.trim()) {
      setError("Please provide a reason for rejection");
      return;
    }

    const token = localStorage.getItem("accessToken");
    if (!token) {
      setError("Authentication token not found.");
      return;
    }

    setProcessingId(selectedSubscription.id);
    try {
      const response = await rejectSubscription(
        selectedSubscription.id,
        rejectReason.trim(),
        token,
      );
      if (response.status === "success") {
        // Refresh the subscriptions list
        const updatedResponse = await getAllSubscriptions(token);
        if (updatedResponse.status === "success") {
          setSubscriptions(updatedResponse.data?.subscriptions || []);
        }
        setShowRejectModal(false);
        setSelectedSubscription(null);
        setRejectReason("");
        setError(null);
      } else {
        setError(response.message || "Failed to reject subscription");
      }
    } catch (err: any) {
      setError(err.message || "Error rejecting subscription");
    } finally {
      setProcessingId(null);
    }
  };

  const handleRejectCancel = () => {
    setShowRejectModal(false);
    setSelectedSubscription(null);
    setRejectReason("");
  };

  const statusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <Badge className="flex items-center gap-1 bg-green-600 text-white">
            <CheckCircle className="h-4 w-4" /> Approved
          </Badge>
        );
      case "rejected":
        return (
          <Badge className="flex items-center gap-1 bg-red-600 text-white">
            <XCircle className="h-4 w-4" /> Rejected
          </Badge>
        );
      default:
        return (
          <Badge className="flex items-center gap-1 bg-yellow-500 text-white">
            <Clock className="h-4 w-4" /> Pending
          </Badge>
        );
    }
  };

  // Filter helper functions
  const handleFilterChange = (
    filterType: keyof typeof filters,
    value: string,
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item: string) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: [],
      paymentMethod: [],
    });
    setSearchTerm("");
  };

  // Export functionality
  const handleExport = (format: "csv" | "json") => {
    const exportColumns: ExportColumn[] = [
      { key: "id", label: "Subscription ID" },
      { key: "ngo.name", label: "NGO Name" },
      { key: "approvedStatus", label: "Status", format: formatStatus },
      { key: "submittedAt", label: "Submitted Date", format: formatDate },
      { key: "expiryDate", label: "Expiry Date", format: formatDate },
      { key: "rejectedReason", label: "Rejected Reason" },
    ];

    const filename = `subscription_payments_${new Date().toISOString().split("T")[0]}`;

    if (format === "csv") {
      downloadCSV(filteredSubscriptions, exportColumns, filename);
    } else {
      downloadJSON(filteredSubscriptions, filename);
    }
  };

  // Filter subscriptions based on search and filters
  const filteredSubscriptions = subscriptions.filter((sub) => {
    const matchesSearch =
      searchTerm === "" ||
      sub.ngo?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sub.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      filters.status.length === 0 ||
      filters.status.includes(sub.approvedStatus);
    const matchesPaymentMethod = filters.paymentMethod.length === 0;

    return matchesSearch && matchesStatus && matchesPaymentMethod;
  });

  const pendingCount = subscriptions.filter(
    (sub) => sub.approvedStatus === "pending",
  ).length;
  const approvedCount = subscriptions.filter(
    (sub) => sub.approvedStatus === "approved",
  ).length;
  const rejectedCount = subscriptions.filter(
    (sub) => sub.approvedStatus === "rejected",
  ).length;

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      {error && (
        <div className="mb-6 rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900 dark:text-red-200">
          <div className="flex items-center gap-2">
            <span>Error: {error}</span>
            <button
              onClick={() => window.location.reload()}
              className="ml-auto text-sm underline hover:no-underline"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className="mb-8 flex flex-col justify-between gap-6 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Subscription Payments Management
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Review and approve NGO annual subscription payments (
            {filteredSubscriptions.length} total)
          </p>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search subscriptions..."
              className="w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <Filter className="h-4 w-4" />
              Filters
              {Object.values(filters).some((arr) => arr.length > 0) && (
                <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {Object.values(filters).flat().length}
                </span>
              )}
            </button>

            <button
              onClick={() => setShowExportModal(true)}
              className="flex items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <Download className="h-4 w-4" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="mb-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-xl bg-gradient-to-br from-blue-50 to-blue-100 p-6 shadow-sm dark:from-blue-900/20 dark:to-blue-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Subscriptions
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {subscriptions.length}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <Eye className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-gradient-to-br from-yellow-50 to-yellow-100 p-6 shadow-sm dark:from-yellow-900/20 dark:to-yellow-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending Approval
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {pendingCount}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
              <Clock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-gradient-to-br from-green-50 to-green-100 p-6 shadow-sm dark:from-green-900/20 dark:to-green-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Approved Payments
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {approvedCount}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-gradient-to-br from-red-50 to-red-100 p-6 shadow-sm dark:from-red-900/20 dark:to-red-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Rejected Payments
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {rejectedCount}
              </p>
            </div>
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900">
              <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </div>
      </div>

      {showFilters && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <div className="flex gap-2">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <XIcon className="h-4 w-4" />
                Clear all
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              >
                <XIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </h4>
              <div className="space-y-2">
                {["pending", "approved", "rejected"].map((status) => (
                  <div key={status} className="flex items-center">
                    <input
                      id={`status-${status}`}
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={() => handleFilterChange("status", status)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`status-${status}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Payment Method
              </h4>
              <div className="space-y-2">
                {["bank_transfer", "mobile_money", "cash", "cheque"].map(
                  (method) => (
                    <div key={method} className="flex items-center">
                      <input
                        id={`method-${method}`}
                        type="checkbox"
                        checked={filters.paymentMethod.includes(method)}
                        onChange={() =>
                          handleFilterChange("paymentMethod", method)
                        }
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                      />
                      <label
                        htmlFor={`method-${method}`}
                        className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >
                        {method.replace(/_/g, " ").charAt(0).toUpperCase() +
                          method.replace(/_/g, " ").slice(1)}
                      </label>
                    </div>
                  ),
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {Object.values(filters).some((arr) => arr.length > 0) && (
        <div className="mb-4 flex flex-wrap gap-2">
          {filters.status.map((status) => (
            <span
              key={`status-${status}`}
              className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              Status: {status.charAt(0).toUpperCase() + status.slice(1)}
              <button
                onClick={() => handleFilterChange("status", status)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-800 hover:bg-blue-200 dark:text-blue-200 dark:hover:bg-blue-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
          {filters.paymentMethod.map((method) => (
            <span
              key={`method-${method}`}
              className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              Method:{" "}
              {method.replace(/_/g, " ").charAt(0).toUpperCase() +
                method.replace(/_/g, " ").slice(1)}
              <button
                onClick={() => handleFilterChange("paymentMethod", method)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-800 hover:bg-green-200 dark:text-green-200 dark:hover:bg-green-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Filter Tabs */}
      {/* <div className="flex w-fit space-x-1 rounded-lg bg-gray-100 p-1 dark:bg-gray-800">
        {[
          { key: "all", label: "All", count: subscriptions.length },
          { key: "pending", label: "Pending", count: pendingCount },
          { key: "approved", label: "Approved", count: approvedCount },
          { key: "rejected", label: "Rejected", count: rejectedCount },
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setStatusFilter(tab.key)}
            className={`rounded-md px-4 py-2 text-sm font-medium transition-colors ${
              statusFilter === tab.key
                ? "bg-white text-gray-900 shadow-sm dark:bg-gray-700 dark:text-white"
                : "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
            }`}
          >
            {tab.label} ({tab.count})
          </button>
        ))}
      </div> */}

      {/* Subscriptions Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredSubscriptions.length === 0 && (
          <div className="col-span-full py-8 text-center">
            <div className="mb-4 text-6xl text-gray-400">📄</div>
            <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
              No subscriptions found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              No subscription payments have been submitted yet.
            </p>
          </div>
        )}
        {filteredSubscriptions.map((sub) => (
          <Card key={sub.id} className="rounded-2xl shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{sub.ngo.name}</span>
                {statusBadge(sub.approvedStatus)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <p>
                <strong>Submitted:</strong>{" "}
                {new Date(sub.submittedAt).toLocaleDateString()}
              </p>
              <p>
                <strong>Expiry:</strong>{" "}
                {new Date(sub.expiryDate).toLocaleDateString()}
              </p>
              <p>
                <strong>Proof of Payment:</strong>{" "}
                <a
                  href={sub.proofOfPaymentUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 underline"
                >
                  View
                </a>
              </p>

              {sub.approvedStatus === "rejected" && sub.rejectedReason && (
                <div className="flex items-start gap-2 text-red-600">
                  <AlertCircle className="mt-1 h-4 w-4" />
                  <span>
                    <strong>Reason:</strong> {sub.rejectedReason}
                  </span>
                </div>
              )}

              {sub.certificate && (
                <div className="rounded-lg border bg-gray-50 p-2 dark:bg-gray-800">
                  <p>
                    <strong>Certificate #:</strong>{" "}
                    {sub.certificate.certificateNumber}
                  </p>
                  <p>
                    <strong>Expires:</strong>{" "}
                    {new Date(sub.certificate.expiryDate).toLocaleDateString()}
                  </p>
                  <div className="mt-2 flex items-center justify-between">
                    <img
                      src={sub.certificate.qrCodeUrl}
                      alt="QR Code"
                      className="h-16 w-16"
                    />
                    <Button
                      variant="outline"
                      onClick={() => onViewCertificate(sub.certificate!.id)}
                    >
                      View Certificate
                    </Button>
                  </div>
                </div>
              )}

              {/* Action Buttons for Financial Officers */}
              {sub.approvedStatus === "pending" && (
                <div className="mt-4 flex gap-2 border-t border-gray-200 pt-3 dark:border-gray-600">
                  <Button
                    onClick={() => handleApprove(sub.id)}
                    disabled={processingId === sub.id}
                    className="flex-1 bg-green-600 text-white hover:bg-green-700"
                    size="sm"
                  >
                    {processingId === sub.id ? (
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                        <span>Processing...</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <ThumbsUp className="h-4 w-4" />
                        <span>Approve</span>
                      </div>
                    )}
                  </Button>
                  <Button
                    onClick={() => handleRejectClick(sub)}
                    disabled={processingId === sub.id}
                    variant="outline"
                    className="flex-1 border-red-300 text-red-600 hover:border-red-400 hover:bg-red-50"
                    size="sm"
                  >
                    <div className="flex items-center gap-2">
                      <ThumbsDown className="h-4 w-4" />
                      <span>Reject</span>
                    </div>
                  </Button>
                </div>
              )}

              {/* View Proof of Payment Button */}
              <div className="mt-3 border-t border-gray-200 pt-2 dark:border-gray-600">
                <Button
                  onClick={() => window.open(sub.proofOfPaymentUrl, "_blank")}
                  variant="outline"
                  className="w-full"
                  size="sm"
                >
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <span>View Payment Proof</span>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Reject Modal */}
        {showRejectModal && selectedSubscription && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="mx-4 w-full max-w-md rounded-lg bg-white p-6 dark:bg-gray-800">
              <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                Reject Subscription
              </h3>
              <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                You are about to reject the subscription from{" "}
                <strong>{selectedSubscription.ngo.name}</strong>. Please provide
                a reason:
              </p>
              <textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                placeholder="Enter reason for rejection..."
                className="h-24 w-full resize-none rounded-lg border border-gray-300 p-3 text-sm dark:border-gray-600"
                maxLength={500}
              />
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {rejectReason.length}/500 characters
              </div>
              <div className="mt-6 flex gap-3">
                <Button
                  onClick={handleRejectCancel}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleRejectSubmit}
                  disabled={
                    !rejectReason.trim() ||
                    processingId === selectedSubscription.id
                  }
                  className="flex-1 bg-red-600 text-white hover:bg-red-700"
                >
                  {processingId === selectedSubscription.id ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                      <span>Rejecting...</span>
                    </div>
                  ) : (
                    "Confirm Reject"
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Export Modal */}
        <ExportModal
          isOpen={showExportModal}
          onClose={() => setShowExportModal(false)}
          onExport={handleExport}
          title="Export Subscription Payments"
          description="Choose the format to export your subscription payment data"
        />
      </div>
    </div>
  );
}
