"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Search,
  Filter,
  ChevronDown,
  Download,
} from "lucide-react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import {
  getAllSubscriptions,
  approveSubscription,
  rejectSubscription,
  Subscription,
} from "@/services/subscription.services";

export default function SubscriptionPaymentsPage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedSubscription, setSelectedSubscription] =
    useState<Subscription | null>(null);
  const [rejectReason, setRejectReason] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: [] as string[],
    paymentMethod: [] as string[],
  });

  useEffect(() => {
    const fetchSubscriptions = async () => {
      try {
        const token = localStorage.getItem("accessToken");
        if (token) {
          const response = await getAllSubscriptions(token);
          if (response.status === "success") {
            setSubscriptions(response.data?.subscriptions || []);
          } else {
            setError(response.message);
          }
        } else {
          setError("Authentication token not found.");
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptions();
  }, []);

  const onViewCertificate = (certificateId: string) => {
    // Handle certificate view logic, e.g., navigate to a certificate page
    console.log("Viewing certificate:", certificateId);
  };

  const handleApprove = async (subscriptionId: string) => {
    const token = localStorage.getItem("accessToken");
    if (!token) {
      setError("Authentication token not found.");
      return;
    }

    setProcessingId(subscriptionId);
    try {
      const response = await approveSubscription(subscriptionId, token);
      if (response.status === "success") {
        // Refresh the subscriptions list
        const updatedResponse = await getAllSubscriptions(token);
        if (updatedResponse.status === "success") {
          setSubscriptions(updatedResponse.data?.subscriptions || []);
        }
        setError(null);
      } else {
        setError(response.message || "Failed to approve subscription");
      }
    } catch (err: any) {
      setError(err.message || "Error approving subscription");
    } finally {
      setProcessingId(null);
    }
  };

  const handleRejectClick = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setShowRejectModal(true);
    setRejectReason("");
  };

  const handleRejectSubmit = async () => {
    if (!selectedSubscription || !rejectReason.trim()) {
      setError("Please provide a reason for rejection");
      return;
    }

    const token = localStorage.getItem("accessToken");
    if (!token) {
      setError("Authentication token not found.");
      return;
    }

    setProcessingId(selectedSubscription.id);
    try {
      const response = await rejectSubscription(
        selectedSubscription.id,
        rejectReason.trim(),
        token,
      );
      if (response.status === "success") {
        // Refresh the subscriptions list
        const updatedResponse = await getAllSubscriptions(token);
        if (updatedResponse.status === "success") {
          setSubscriptions(updatedResponse.data?.subscriptions || []);
        }
        setShowRejectModal(false);
        setSelectedSubscription(null);
        setRejectReason("");
        setError(null);
      } else {
        setError(response.message || "Failed to reject subscription");
      }
    } catch (err: any) {
      setError(err.message || "Error rejecting subscription");
    } finally {
      setProcessingId(null);
    }
  };

  const handleRejectCancel = () => {
    setShowRejectModal(false);
    setSelectedSubscription(null);
    setRejectReason("");
  };

  const statusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <Badge className="flex items-center gap-1 bg-green-600 text-white">
            <CheckCircle className="h-4 w-4" /> Approved
          </Badge>
        );
      case "rejected":
        return (
          <Badge className="flex items-center gap-1 bg-red-600 text-white">
            <XCircle className="h-4 w-4" /> Rejected
          </Badge>
        );
      default:
        return (
          <Badge className="flex items-center gap-1 bg-yellow-500 text-white">
            <Clock className="h-4 w-4" /> Pending
          </Badge>
        );
    }
  };

  // Filter helper functions
  const handleFilterChange = (
    filterType: keyof typeof filters,
    value: string,
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item: string) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: [],
      paymentMethod: [],
    });
    setSearchTerm("");
  };

  // Filter subscriptions based on search and filters
  const filteredSubscriptions = subscriptions.filter((sub) => {
    const matchesSearch =
      searchTerm === "" ||
      sub.ngo?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sub.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      filters.status.length === 0 ||
      filters.status.includes(sub.approvedStatus);
    const matchesPaymentMethod =
      filters.paymentMethod.length === 0 ||
      (sub.paymentMethod && filters.paymentMethod.includes(sub.paymentMethod));

    return matchesSearch && matchesStatus && matchesPaymentMethod;
  });

  const pendingCount = subscriptions.filter(
    (sub) => sub.approvedStatus === "pending",
  ).length;
  const approvedCount = subscriptions.filter(
    (sub) => sub.approvedStatus === "approved",
  ).length;
  const rejectedCount = subscriptions.filter(
    (sub) => sub.approvedStatus === "rejected",
  ).length;

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Subscription Payments Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Review and approve NGO annual subscription payments
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {subscriptions.length}
              </p>
            </div>
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
              <Eye className="h-4 w-4 text-blue-600" />
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending
              </p>
              <p className="text-2xl font-bold text-yellow-600">
                {pendingCount}
              </p>
            </div>
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/30">
              <Clock className="h-4 w-4 text-yellow-600" />
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Approved
              </p>
              <p className="text-2xl font-bold text-green-600">
                {approvedCount}
              </p>
            </div>
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
              <CheckCircle className="h-4 w-4 text-green-600" />
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Rejected
              </p>
              <p className="text-2xl font-bold text-red-600">{rejectedCount}</p>
            </div>
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
              <XCircle className="h-4 w-4 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-1 items-center gap-2">
          <div className="relative max-w-md flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search subscriptions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:hover:bg-gray-700"
          >
            <Filter className="h-4 w-4" />
            Filters
            <ChevronDown
              className={`h-4 w-4 transition-transform ${showFilters ? "rotate-180" : ""}`}
            />
          </button>
          <button className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
            <Download className="h-4 w-4" />
            Export
          </button>
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-600 dark:bg-gray-700">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <button
              onClick={clearFilters}
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400"
            >
              Clear All
            </button>
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            {/* Status Filter */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </label>
              <div className="space-y-2">
                {["pending", "approved", "rejected"].map((status) => (
                  <label key={status} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={() => handleFilterChange("status", status)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
                      {status}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Payment Method Filter */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Payment Method
              </label>
              <div className="space-y-2">
                {["bank_transfer", "mobile_money", "cash", "cheque"].map(
                  (method) => (
                    <label key={method} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.paymentMethod.includes(method)}
                        onChange={() =>
                          handleFilterChange("paymentMethod", method)
                        }
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
                        {method.replace(/_/g, " ")}
                      </span>
                    </label>
                  ),
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filter Tabs */}
      {/* <div className="flex w-fit space-x-1 rounded-lg bg-gray-100 p-1 dark:bg-gray-800">
        {[
          { key: "all", label: "All", count: subscriptions.length },
          { key: "pending", label: "Pending", count: pendingCount },
          { key: "approved", label: "Approved", count: approvedCount },
          { key: "rejected", label: "Rejected", count: rejectedCount },
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setStatusFilter(tab.key)}
            className={`rounded-md px-4 py-2 text-sm font-medium transition-colors ${
              statusFilter === tab.key
                ? "bg-white text-gray-900 shadow-sm dark:bg-gray-700 dark:text-white"
                : "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
            }`}
          >
            {tab.label} ({tab.count})
          </button>
        ))}
      </div> */}

      {/* Subscriptions Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredSubscriptions.length === 0 && (
          <div className="col-span-full py-8 text-center">
            <div className="mb-4 text-6xl text-gray-400">📄</div>
            <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
              No subscriptions found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {statusFilter === "all"
                ? "No subscription payments have been submitted yet."
                : `No ${statusFilter} subscriptions found.`}
            </p>
          </div>
        )}
        {filteredSubscriptions.map((sub) => (
          <Card key={sub.id} className="rounded-2xl shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{sub.ngo.name}</span>
                {statusBadge(sub.approvedStatus)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <p>
                <strong>Submitted:</strong>{" "}
                {new Date(sub.submittedAt).toLocaleDateString()}
              </p>
              <p>
                <strong>Expiry:</strong>{" "}
                {new Date(sub.expiryDate).toLocaleDateString()}
              </p>
              <p>
                <strong>Proof of Payment:</strong>{" "}
                <a
                  href={sub.proofOfPaymentUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 underline"
                >
                  View
                </a>
              </p>

              {sub.approvedStatus === "rejected" && sub.rejectedReason && (
                <div className="flex items-start gap-2 text-red-600">
                  <AlertCircle className="mt-1 h-4 w-4" />
                  <span>
                    <strong>Reason:</strong> {sub.rejectedReason}
                  </span>
                </div>
              )}

              {sub.certificate && (
                <div className="rounded-lg border bg-gray-50 p-2 dark:bg-gray-800">
                  <p>
                    <strong>Certificate #:</strong>{" "}
                    {sub.certificate.certificateNumber}
                  </p>
                  <p>
                    <strong>Expires:</strong>{" "}
                    {new Date(sub.certificate.expiryDate).toLocaleDateString()}
                  </p>
                  <div className="mt-2 flex items-center justify-between">
                    <img
                      src={sub.certificate.qrCodeUrl}
                      alt="QR Code"
                      className="h-16 w-16"
                    />
                    <Button
                      variant="outline"
                      onClick={() => onViewCertificate(sub.certificate!.id)}
                    >
                      View Certificate
                    </Button>
                  </div>
                </div>
              )}

              {/* Action Buttons for Financial Officers */}
              {sub.approvedStatus === "pending" && (
                <div className="mt-4 flex gap-2 border-t border-gray-200 pt-3 dark:border-gray-600">
                  <Button
                    onClick={() => handleApprove(sub.id)}
                    disabled={processingId === sub.id}
                    className="flex-1 bg-green-600 text-white hover:bg-green-700"
                    size="sm"
                  >
                    {processingId === sub.id ? (
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                        <span>Processing...</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <ThumbsUp className="h-4 w-4" />
                        <span>Approve</span>
                      </div>
                    )}
                  </Button>
                  <Button
                    onClick={() => handleRejectClick(sub)}
                    disabled={processingId === sub.id}
                    variant="outline"
                    className="flex-1 border-red-300 text-red-600 hover:border-red-400 hover:bg-red-50"
                    size="sm"
                  >
                    <div className="flex items-center gap-2">
                      <ThumbsDown className="h-4 w-4" />
                      <span>Reject</span>
                    </div>
                  </Button>
                </div>
              )}

              {/* View Proof of Payment Button */}
              <div className="mt-3 border-t border-gray-200 pt-2 dark:border-gray-600">
                <Button
                  onClick={() => window.open(sub.proofOfPaymentUrl, "_blank")}
                  variant="outline"
                  className="w-full"
                  size="sm"
                >
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <span>View Payment Proof</span>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Reject Modal */}
        {showRejectModal && selectedSubscription && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="mx-4 w-full max-w-md rounded-lg bg-white p-6 dark:bg-gray-800">
              <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                Reject Subscription
              </h3>
              <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                You are about to reject the subscription from{" "}
                <strong>{selectedSubscription.ngo.name}</strong>. Please provide
                a reason:
              </p>
              <textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                placeholder="Enter reason for rejection..."
                className="h-24 w-full resize-none rounded-lg border border-gray-300 p-3 text-sm dark:border-gray-600"
                maxLength={500}
              />
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {rejectReason.length}/500 characters
              </div>
              <div className="mt-6 flex gap-3">
                <Button
                  onClick={handleRejectCancel}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleRejectSubmit}
                  disabled={
                    !rejectReason.trim() ||
                    processingId === selectedSubscription.id
                  }
                  className="flex-1 bg-red-600 text-white hover:bg-red-700"
                >
                  {processingId === selectedSubscription.id ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                      <span>Rejecting...</span>
                    </div>
                  ) : (
                    "Confirm Reject"
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
