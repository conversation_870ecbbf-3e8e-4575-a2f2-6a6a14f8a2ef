"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Download,
  Eye,
  FileText,
  CheckCircle,
  Clock,
} from "lucide-react";
import ExportModal from "@/components/ExportModal";
import {
  downloadCSV,
  downloadJSON,
  formatCurrency,
  formatDate,
  formatStatus,
  ExportColumn,
} from "@/utils/exportUtils";
import { getReceipts, IReceipt } from "@/services/receipts.services";

export default function ReceiptsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [receiptTypeFilter, setReceiptTypeFilter] = useState("all");
  const [showExportModal, setShowExportModal] = useState(false);
  const [receipts, setReceipts] = useState<IReceipt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // For tracking total amounts and counts
  const [stats, setStats] = useState({
    totalReceipts: 0,
    issuedReceipts: 0,
    pendingReceipts: 0,
    totalAmount: 0,
  });

  useEffect(() => {
    async function fetchReceipts() {
      try {
        setLoading(true);
        // Get token from localStorage (browser-only code)
        const token =
          typeof window !== "undefined"
            ? localStorage.getItem("accessToken") || ""
            : "";

        if (!token) {
          setError("Authentication token not found. Please log in.");
          setLoading(false);
          return;
        }

        const response = await getReceipts({}, token);

        if (response.status === "success" && response.data) {
          setReceipts(response.data);

          // Calculate statistics
          const issued = response.data.filter((r) => r.receiptUrl).length; // Assuming receipts with URLs are issued
          const pending = response.data.length - issued;
          const totalAmount = response.data.reduce(
            (sum, receipt) => sum + receipt.amount,
            0,
          );

          setStats({
            totalReceipts: response.data.length,
            issuedReceipts: issued,
            pendingReceipts: pending,
            totalAmount: totalAmount,
          });
        } else {
          setError("Failed to fetch receipts");
        }
      } catch (err) {
        setError("An error occurred while fetching receipts");
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchReceipts();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleReceiptTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setReceiptTypeFilter(e.target.value);
  };

  // Export functionality
  const handleExport = (format: "csv" | "json") => {
    const exportColumns: ExportColumn[] = [
      { key: "receiptNumber", label: "Receipt Number" },
      { key: "ngoName", label: "NGO Name" },
      { key: "type", label: "Receipt Type" },
      { key: "amount", label: "Amount", format: formatCurrency },
      { key: "status", label: "Status", format: formatStatus },
      { key: "issuedDate", label: "Issued Date", format: formatDate },
      { key: "description", label: "Description" },
    ];

    const filename = `receipts_${new Date().toISOString().split("T")[0]}`;

    if (format === "csv") {
      downloadCSV(filteredReceipts, exportColumns, filename);
    } else {
      downloadJSON(filteredReceipts, filename);
    }
  };

  // Filter receipts based on search term and status
  const filteredReceipts = receipts.filter((receipt) => {
    // Status filter
    if (
      receiptTypeFilter !== "all" &&
      (receiptTypeFilter === "processing fee" ||
        receiptTypeFilter === "registration fee" ||
        receiptTypeFilter === "subscription fee")
    ) {
      return false;
    }

    // Search term filter (search in receipt number or ngo name)
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        receipt.receiptNumber.toLowerCase().includes(searchLower) ||
        receipt.ngo.name.toLowerCase().includes(searchLower)
      );
    }

    return true;
  });

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      {error && (
        <div className="mb-6 rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900 dark:text-red-200">
          <div className="flex items-center gap-2">
            <span>Error: {error}</span>
            <button
              onClick={() => window.location.reload()}
              className="ml-auto text-sm underline hover:no-underline"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className="mb-8 flex flex-col justify-between gap-6 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Receipt Management
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Generate and manage payment receipts ({filteredReceipts.length}{" "}
            total)
          </p>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={handleSearch}
              placeholder="Search receipts..."
              className="w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
          <div className="flex gap-2">
            <select
              className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              value={receiptTypeFilter}
              onChange={handleReceiptTypeChange}
            >
              <option value="all">All Receipt Types</option>
              <option value="subscription fee">Subscription Fees</option>
              <option value="processing fee">Processing Fees</option>
              <option value="registration fee">Registration Fees</option>
            </select>

            <button
              onClick={() => setShowExportModal(true)}
              className="flex items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <Download className="h-4 w-4" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="mb-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <div className="rounded-xl bg-gradient-to-br from-blue-50 to-blue-100 p-6 shadow-sm dark:from-blue-900/20 dark:to-blue-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Receipts
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.totalReceipts}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-gradient-to-br from-green-50 to-green-100 p-6 shadow-sm dark:from-green-900/20 dark:to-green-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Issued Receipts
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.issuedReceipts}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-gradient-to-br from-yellow-50 to-yellow-100 p-6 shadow-sm dark:from-yellow-900/20 dark:to-yellow-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Amount
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                MWK {stats.totalAmount.toLocaleString()}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
              <FileText className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm dark:border-gray-700">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Clock className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                Loading receipts...
              </span>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead>
                <tr className="bg-gray-50 dark:bg-gray-800">
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Receipt #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    NGO Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Issue Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                {filteredReceipts.length > 0 ? (
                  filteredReceipts.map((receipt) => (
                    <tr
                      key={receipt._id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    >
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                        {receipt.receiptNumber}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                        {receipt.ngo.name}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                        MWK {receipt.amount.toLocaleString()}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-center">
                        <span className="inline-flex items-center rounded-full border border-gray-300 bg-gray-50 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300">
                          {receipt.type ? receipt.type : "N/A"}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                        {new Date(receipt.issueDate).toLocaleDateString()}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            className="inline-flex items-center rounded-md bg-blue-100 p-2 text-blue-600 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-blue-900 dark:text-blue-400 dark:hover:bg-blue-800"
                            title="View Receipt Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          {receipt.receiptUrl && (
                            <a
                              href={receipt.receiptUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center rounded-md bg-green-100 p-2 text-green-600 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-green-900 dark:text-green-400 dark:hover:bg-green-800"
                              title="Download Receipt"
                            >
                              <Download className="h-4 w-4" />
                            </a>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="py-12 text-center text-gray-500">
                      <div className="flex flex-col items-center justify-center gap-2">
                        <FileText className="h-8 w-8 text-gray-400" />
                        <p className="text-lg font-medium">No receipts found</p>
                        <p className="text-sm text-gray-500">
                          Try adjusting your search or filters
                        </p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Export Modal */}
      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        onExport={handleExport}
        title="Export Receipts"
        description="Choose the format to export your receipt data"
      />
    </div>
  );
}
