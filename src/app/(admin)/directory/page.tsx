// "use client";

// import React, { useState, useEffect } from "react";
// import { useRouter } from "next/navigation";
// import {
//   Table,
//   TableHeader,
//   TableBody,
//   TableRow,
//   TableHead,
//   TableCell,
// } from "@/components/ui/table";
// import {
//   SearchIcon,
//   EyeIcon,
//   MoreVertical,
//   CheckCircle,
//   XCircle,
//   Clock,
//   FilterIcon,
//   XIcon,
//   ChevronDown,
//   Loader2,
//   Building2,
//   Users,
//   MapPin,
// } from "lucide-react";
// import { format } from "date-fns";
// import { getAllNGOs, INgo, searchNGOs } from "@/services/ngo.services";

// const statusOptions = ["active", "inactive"];
// const approvalStatusOptions = ["approved", "pending", "rejected"];
// const typeOptions = ["local", "international"];

// export default function DirectoryPage() {
//   const router = useRouter();
//   const [ngos, setNgos] = useState<INgo[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);
//   const [search, setSearch] = useState("");
//   const [showFilters, setShowFilters] = useState(false);
//   const [filters, setFilters] = useState({
//     status: [] as string[],
//     approvedStatus: [] as string[],
//     type: [] as string[],
//   });
//   const [totalCount, setTotalCount] = useState(0);
//   const [currentPage, setCurrentPage] = useState(1);
//   const [itemsPerPage] = useState(10);

//   useEffect(() => {
//     fetchNGOs();
//   }, [currentPage, filters]);

//   const fetchNGOs = async () => {
//     try {
//       setLoading(true);
//       setError(null);

//       const token = localStorage.getItem("accessToken");
//       if (!token) {
//         setError("Authentication required");
//         return;
//       }

//       const params = {
//         page: currentPage,
//         limit: itemsPerPage,
//         ...(filters.status.length > 0 && { status: filters.status.join(",") }),
//         ...(filters.approvedStatus.length > 0 && {
//           approvedStatus: filters.approvedStatus.join(","),
//         }),
//         ...(filters.type.length > 0 && { type: filters.type.join(",") }),
//       };

//       const response = await getAllNGOs(params, token);

//       if (response.status === "success" && response.data) {
//         setNgos(response.data);
//         setTotalCount(response.pagination?.total || response.data.length);
//       } else {
//         setError(response.message || "Failed to fetch organizations");
//       }
//     } catch (err) {
//       setError("An error occurred while fetching organizations");
//       console.error(err);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleSearch = async () => {
//     if (!search.trim()) {
//       fetchNGOs();
//       return;
//     }

//     try {
//       setLoading(true);
//       const token = localStorage.getItem("accessToken");
//       if (!token) return;

//       const response = await searchNGOs(
//         {
//           name: search,
//           page: currentPage,
//           limit: itemsPerPage,
//         },
//         token,
//       );

//       if (response.status === "success" && response.data) {
//         setNgos(response.data);
//         setTotalCount(response.pagination?.total || response.data.length);
//       }
//     } catch (err) {
//       console.error("Search error:", err);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleFilterChange = (filterType: string, value: string) => {
//     setFilters((prev) => ({
//       ...prev,
//       [filterType]: prev[filterType].includes(value)
//         ? prev[filterType].filter((item) => item !== value)
//         : [...prev[filterType], value],
//     }));
//   };

//   const clearFilters = () => {
//     setFilters({
//       status: [],
//       approvedStatus: [],
//       type: [],
//     });
//   };

//   const getStatusColor = (status: string) => {
//     switch (status?.toLowerCase()) {
//       case "approved":
//         return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
//       case "pending":
//         return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
//       case "rejected":
//         return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
//       case "active":
//         return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
//       case "inactive":
//         return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
//       default:
//         return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
//     }
//   };

//   const getStatusIcon = (status: string) => {
//     switch (status?.toLowerCase()) {
//       case "approved":
//       case "active":
//         return <CheckCircle className="h-4 w-4 text-green-600" />;
//       case "rejected":
//       case "inactive":
//         return <XCircle className="h-4 w-4 text-red-600" />;
//       case "pending":
//         return <Clock className="h-4 w-4 text-yellow-600" />;
//       default:
//         return <Clock className="h-4 w-4 text-gray-600" />;
//     }
//   };

//   const handleViewOrganization = (ngoId: string) => {
//     router.push(`/organizations/${ngoId}`);
//   };

//   const totalPages = Math.ceil(totalCount / itemsPerPage);

//   return (
//     <div className="space-y-6">
//       {/* Header */}
//       <div className="flex items-center justify-between">
//         <div>
//           <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
//             Organization Directory
//           </h1>
//           <p className="text-gray-600 dark:text-gray-400">
//             Complete directory of all registered organizations
//           </p>
//         </div>
//         <div className="flex items-center gap-2">
//           <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
//             <Building2 className="h-4 w-4" />
//             <span>{totalCount} Organizations</span>
//           </div>
//         </div>
//       </div>

//       {/* Search and Filters */}
//       <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
//         <div className="flex flex-1 items-center gap-2">
//           <div className="relative max-w-md flex-1">
//             <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
//             <input
//               type="text"
//               placeholder="Search organizations..."
//               value={search}
//               onChange={(e) => setSearch(e.target.value)}
//               onKeyPress={(e) => e.key === "Enter" && handleSearch()}
//               className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
//             />
//           </div>
//           <button
//             onClick={handleSearch}
//             className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
//           >
//             Search
//           </button>
//         </div>

//         <div className="flex items-center gap-2">
//           <button
//             onClick={() => setShowFilters(!showFilters)}
//             className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:hover:bg-gray-700"
//           >
//             <FilterIcon className="h-4 w-4" />
//             Filters
//             <ChevronDown
//               className={`h-4 w-4 transition-transform ${showFilters ? "rotate-180" : ""}`}
//             />
//           </button>
//           <button className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
//             <MoreVertical className="h-4 w-4" />
//             Export
//           </button>
//         </div>
//       </div>

//       {/* Filters Panel */}
//       {showFilters && (
//         <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-600 dark:bg-gray-700">
//           <div className="mb-4 flex items-center justify-between">
//             <h3 className="text-lg font-medium text-gray-900 dark:text-white">
//               Filters
//             </h3>
//             <button
//               onClick={clearFilters}
//               className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400"
//             >
//               Clear All
//             </button>
//           </div>

//           <div className="grid gap-4 sm:grid-cols-3">
//             {/* Status Filter */}
//             <div>
//               <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
//                 Status
//               </label>
//               <div className="space-y-2">
//                 {statusOptions.map((status) => (
//                   <label key={status} className="flex items-center">
//                     <input
//                       type="checkbox"
//                       checked={filters.status.includes(status)}
//                       onChange={() => handleFilterChange("status", status)}
//                       className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
//                     />
//                     <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
//                       {status}
//                     </span>
//                   </label>
//                 ))}
//               </div>
//             </div>

//             {/* Approval Status Filter */}
//             <div>
//               <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
//                 Approval Status
//               </label>
//               <div className="space-y-2">
//                 {approvalStatusOptions.map((status) => (
//                   <label key={status} className="flex items-center">
//                     <input
//                       type="checkbox"
//                       checked={filters.approvedStatus.includes(status)}
//                       onChange={() =>
//                         handleFilterChange("approvedStatus", status)
//                       }
//                       className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
//                     />
//                     <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
//                       {status}
//                     </span>
//                   </label>
//                 ))}
//               </div>
//             </div>

//             {/* Type Filter */}
//             <div>
//               <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
//                 Type
//               </label>
//               <div className="space-y-2">
//                 {typeOptions.map((type) => (
//                   <label key={type} className="flex items-center">
//                     <input
//                       type="checkbox"
//                       checked={filters.type.includes(type)}
//                       onChange={() => handleFilterChange("type", type)}
//                       className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
//                     />
//                     <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
//                       {type}
//                     </span>
//                   </label>
//                 ))}
//               </div>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Error Message */}
//       {error && (
//         <div className="rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
//           {error}
//         </div>
//       )}

//       {/* Organizations Table */}
//       <div className="rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
//         <div className="overflow-x-auto">
//           <Table>
//             <TableHeader>
//               <TableRow>
//                 <TableHead>Organization</TableHead>
//                 <TableHead>Type</TableHead>
//                 <TableHead>Chief Executive</TableHead>
//                 <TableHead>Location</TableHead>
//                 <TableHead>Status</TableHead>
//                 <TableHead>Approval</TableHead>
//                 <TableHead>Date Founded</TableHead>
//                 <TableHead className="text-right">Actions</TableHead>
//               </TableRow>
//             </TableHeader>
//             <TableBody>
//               {loading ? (
//                 Array.from({ length: 5 }).map((_, index) => (
//                   <TableRow key={index}>
//                     <TableCell>
//                       <div className="flex items-center gap-3">
//                         <div className="h-10 w-10 animate-pulse rounded-full bg-gray-300 dark:bg-gray-600"></div>
//                         <div>
//                           <div className="h-4 w-32 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
//                           <div className="mt-1 h-3 w-24 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
//                         </div>
//                       </div>
//                     </TableCell>
//                     <TableCell>
//                       <div className="h-4 w-16 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
//                     </TableCell>
//                     <TableCell>
//                       <div className="h-4 w-28 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
//                     </TableCell>
//                     <TableCell>
//                       <div className="h-4 w-20 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
//                     </TableCell>
//                     <TableCell>
//                       <div className="h-6 w-16 animate-pulse rounded-full bg-gray-300 dark:bg-gray-600"></div>
//                     </TableCell>
//                     <TableCell>
//                       <div className="h-6 w-16 animate-pulse rounded-full bg-gray-300 dark:bg-gray-600"></div>
//                     </TableCell>
//                     <TableCell>
//                       <div className="h-4 w-20 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
//                     </TableCell>
//                     <TableCell>
//                       <div className="h-8 w-8 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
//                     </TableCell>
//                   </TableRow>
//                 ))
//               ) : ngos.length === 0 ? (
//                 <TableRow>
//                   <TableCell colSpan={8} className="py-8 text-center">
//                     <div className="flex flex-col items-center gap-2">
//                       <Building2 className="h-12 w-12 text-gray-400" />
//                       <p className="text-gray-500 dark:text-gray-400">
//                         {search
//                           ? "No organizations found matching your search."
//                           : "No organizations found."}
//                       </p>
//                     </div>
//                   </TableCell>
//                 </TableRow>
//               ) : (
//                 ngos.map((ngo) => (
//                   <TableRow key={ngo._id}>
//                     <TableCell>
//                       <div className="flex items-center gap-3">
//                         <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
//                           <Building2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
//                         </div>
//                         <div>
//                           <p className="font-medium text-gray-900 dark:text-white">
//                             {ngo.name}
//                           </p>
//                           <p className="text-sm text-gray-500 dark:text-gray-400">
//                             {ngo.initials}
//                           </p>
//                         </div>
//                       </div>
//                     </TableCell>
//                     <TableCell>
//                       <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium capitalize text-gray-800 dark:bg-gray-700 dark:text-gray-300">
//                         {ngo.type}
//                       </span>
//                     </TableCell>
//                     <TableCell>
//                       <div>
//                         <p className="text-sm font-medium text-gray-900 dark:text-white">
//                           {ngo.chiefExecutiveName}
//                         </p>
//                         <p className="text-xs text-gray-500 dark:text-gray-400">
//                           {ngo.chiefExecutiveEmail}
//                         </p>
//                       </div>
//                     </TableCell>
//                     <TableCell>
//                       <div className="flex items-center gap-1">
//                         <MapPin className="h-3 w-3 text-gray-400" />
//                         <span className="text-sm text-gray-600 dark:text-gray-400">
//                           {ngo.headquartersAddress?.split(",")[0] || "N/A"}
//                         </span>
//                       </div>
//                     </TableCell>
//                     <TableCell>
//                       <span
//                         className={`inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(ngo.status || "inactive")}`}
//                       >
//                         {getStatusIcon(ngo.status || "inactive")}
//                         {(ngo.status || "inactive").charAt(0).toUpperCase() +
//                           (ngo.status || "inactive").slice(1)}
//                       </span>
//                     </TableCell>
//                     <TableCell>
//                       <span
//                         className={`inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(ngo.approvedStatus || "pending")}`}
//                       >
//                         {getStatusIcon(ngo.approvedStatus || "pending")}
//                         {(ngo.approvedStatus || "pending")
//                           .charAt(0)
//                           .toUpperCase() +
//                           (ngo.approvedStatus || "pending").slice(1)}
//                       </span>
//                     </TableCell>
//                     <TableCell>
//                       <span className="text-sm text-gray-600 dark:text-gray-400">
//                         {ngo.dateFounded
//                           ? format(new Date(ngo.dateFounded), "MMM dd, yyyy")
//                           : "N/A"}
//                       </span>
//                     </TableCell>
//                     <TableCell className="text-right">
//                       <div className="flex items-center justify-end gap-2">
//                         <button
//                           onClick={() => handleViewOrganization(ngo._id)}
//                           className="rounded-lg p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300"
//                           title="View Details"
//                         >
//                           <EyeIcon className="h-4 w-4" />
//                         </button>
//                       </div>
//                     </TableCell>
//                   </TableRow>
//                 ))
//               )}
//             </TableBody>
//           </Table>
//         </div>

//         {/* Pagination */}
//         {!loading && ngos.length > 0 && totalPages > 1 && (
//           <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 dark:border-gray-700 dark:bg-gray-800 sm:px-6">
//             <div className="flex flex-1 justify-between sm:hidden">
//               <button
//                 onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
//                 disabled={currentPage === 1}
//                 className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
//               >
//                 Previous
//               </button>
//               <button
//                 onClick={() =>
//                   setCurrentPage((prev) => Math.min(prev + 1, totalPages))
//                 }
//                 disabled={currentPage === totalPages}
//                 className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
//               >
//                 Next
//               </button>
//             </div>
//             <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
//               <div>
//                 <p className="text-sm text-gray-700 dark:text-gray-300">
//                   Showing{" "}
//                   <span className="font-medium">
//                     {(currentPage - 1) * itemsPerPage + 1}
//                   </span>{" "}
//                   to{" "}
//                   <span className="font-medium">
//                     {Math.min(currentPage * itemsPerPage, totalCount)}
//                   </span>{" "}
//                   of <span className="font-medium">{totalCount}</span> results
//                 </p>
//               </div>
//               <div>
//                 <nav
//                   className="isolate inline-flex -space-x-px rounded-md shadow-sm"
//                   aria-label="Pagination"
//                 >
//                   <button
//                     onClick={() =>
//                       setCurrentPage((prev) => Math.max(prev - 1, 1))
//                     }
//                     disabled={currentPage === 1}
//                     className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:cursor-not-allowed disabled:opacity-50 dark:ring-gray-600 dark:hover:bg-gray-700"
//                   >
//                     <span className="sr-only">Previous</span>
//                     <ChevronDown
//                       className="h-5 w-5 rotate-90"
//                       aria-hidden="true"
//                     />
//                   </button>

//                   {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
//                     const pageNumber = i + 1;
//                     return (
//                       <button
//                         key={pageNumber}
//                         onClick={() => setCurrentPage(pageNumber)}
//                         className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
//                           currentPage === pageNumber
//                             ? "z-10 bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
//                             : "text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 dark:text-gray-300 dark:ring-gray-600 dark:hover:bg-gray-700"
//                         }`}
//                       >
//                         {pageNumber}
//                       </button>
//                     );
//                   })}

//                   <button
//                     onClick={() =>
//                       setCurrentPage((prev) => Math.min(prev + 1, totalPages))
//                     }
//                     disabled={currentPage === totalPages}
//                     className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:cursor-not-allowed disabled:opacity-50 dark:ring-gray-600 dark:hover:bg-gray-700"
//                   >
//                     <span className="sr-only">Next</span>
//                     <ChevronDown
//                       className="h-5 w-5 -rotate-90"
//                       aria-hidden="true"
//                     />
//                   </button>
//                 </nav>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// }

"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import {
  SearchIcon,
  EyeIcon,
  MoreVertical,
  CheckCircle,
  XCircle,
  Clock,
  FilterIcon,
  XIcon,
  ChevronDown,
  Loader2,
  Building2,
  Users,
  MapPin,
} from "lucide-react";
import { format } from "date-fns";
import { getAllNGOs, INgo, searchNGOs } from "@/services/ngo.services";

const statusOptions = ["active", "inactive"];
const approvalStatusOptions = ["approved", "pending", "rejected"];
const typeOptions = ["local", "international"];

export default function DirectoryPage() {
  const router = useRouter();
  const [ngos, setNgos] = useState<INgo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: [] as string[],
    approvedStatus: [] as string[],
    type: [] as string[],
  });
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  useEffect(() => {
    fetchNGOs();
  }, [currentPage, filters]);

  const fetchNGOs = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem("accessToken");
      if (!token) {
        setError("Authentication required");
        return;
      }

      const params = {
        page: currentPage,
        limit: itemsPerPage,
        ...(filters.status.length > 0 && { status: filters.status.join(",") }),
        ...(filters.approvedStatus.length > 0 && {
          approvedStatus: filters.approvedStatus.join(","),
        }),
        ...(filters.type.length > 0 && { type: filters.type.join(",") }),
      };

      const response = await getAllNGOs(params, token);

      if (response.status === "success" && response.data) {
        setNgos(response.data);
        setTotalCount(response.pagination?.total || response.data.length);
      } else {
        setError(response.message || "Failed to fetch organizations");
      }
    } catch (err) {
      setError("An error occurred while fetching organizations");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!search.trim()) {
      fetchNGOs();
      return;
    }

    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");
      if (!token) return;

      const response = await searchNGOs(
        {
          name: search,
          page: currentPage,
          limit: itemsPerPage,
        },
        token,
      );

      if (response.status === "success" && response.data) {
        setNgos(response.data);
        setTotalCount(response.pagination?.total || response.data.length);
      }
    } catch (err) {
      console.error("Search error:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterType: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: [],
      approvedStatus: [],
      type: [],
    });
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "active":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "inactive":
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case "approved":
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "rejected":
      case "inactive":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const handleViewOrganization = (ngoId: string) => {
    router.push(`/organizations/${ngoId}`);
  };

  const totalPages = Math.ceil(totalCount / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    const startPage = Math.max(
      1,
      currentPage - Math.floor(maxVisiblePages / 2),
    );
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="mt-6 flex items-center justify-between">
        <div className="text-sm text-gray-700 dark:text-gray-300">
          Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
          {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount}{" "}
          results
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            Previous
          </button>
          {pages.map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`rounded-md px-3 py-2 text-sm font-medium ${
                page === currentPage
                  ? "bg-blue-600 text-white"
                  : "border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
              }`}
            >
              {page}
            </button>
          ))}
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            Next
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      {error && (
        <div className="mb-6 rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
          <div className="flex items-center gap-2">
            <span>Error: {error}</span>
            <button
              onClick={fetchNGOs}
              className="ml-auto text-sm underline hover:no-underline"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className="mb-8 flex flex-col justify-between gap-6 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Organization Directory
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Complete directory of all registered organizations ({totalCount}{" "}
            total)
          </p>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search organizations..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              className="w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <FilterIcon className="h-4 w-4" />
              Filters
              {(filters.status.length > 0 ||
                filters.approvedStatus.length > 0 ||
                filters.type.length > 0) && (
                <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {filters.status.length +
                    filters.approvedStatus.length +
                    filters.type.length}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>

      {showFilters && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <div className="flex gap-2">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <XIcon className="h-4 w-4" />
                Clear all
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              >
                <XIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-3">
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </label>
              <div className="space-y-2">
                {statusOptions.map((status) => (
                  <label key={status} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={() => handleFilterChange("status", status)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
                      {status}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Approval Status
              </label>
              <div className="space-y-2">
                {approvalStatusOptions.map((status) => (
                  <label key={status} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.approvedStatus.includes(status)}
                      onChange={() =>
                        handleFilterChange("approvedStatus", status)
                      }
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
                      {status}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Type
              </label>
              <div className="space-y-2">
                {typeOptions.map((type) => (
                  <label key={type} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.type.includes(type)}
                      onChange={() => handleFilterChange("type", type)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
                      {type}
                    </span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm dark:border-gray-700">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                Loading organizations...
              </span>
            </div>
          ) : (
            <Table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <TableHeader>
                <TableRow className="bg-gray-50 dark:bg-gray-800">
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Organization
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Type
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Chief Executive
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Location
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Status
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Approval
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Date Founded
                  </TableHead>
                  <TableHead className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                {ngos.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      className="py-12 text-center text-gray-500"
                    >
                      <div className="flex flex-col items-center justify-center gap-2">
                        <Building2 className="h-8 w-8 text-gray-400" />
                        <p className="text-lg font-medium">
                          No organizations found
                        </p>
                        <p className="text-sm text-gray-500">
                          {search
                            ? "Try adjusting your search or filters"
                            : "No organizations available"}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  ngos.map((ngo) => (
                    <TableRow
                      key={ngo._id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    >
                      <TableCell className="whitespace-nowrap px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                            <Building2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">
                              {ngo.name}
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {ngo.initials}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4">
                        <span className="inline-flex items-center rounded-full border border-gray-300 bg-gray-50 px-2.5 py-0.5 text-xs font-medium capitalize text-gray-800 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300">
                          {ngo.type}
                        </span>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4">
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {ngo.chiefExecutiveName}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {ngo.chiefExecutiveEmail}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-gray-400" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {ngo.headquartersAddress?.split(",")[0] || "N/A"}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4">
                        <span
                          className={`inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(ngo.status || "inactive")}`}
                        >
                          {getStatusIcon(ngo.status || "inactive")}
                          {(ngo.status || "inactive").charAt(0).toUpperCase() +
                            (ngo.status || "inactive").slice(1)}
                        </span>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4">
                        <span
                          className={`inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(ngo.approvedStatus || "pending")}`}
                        >
                          {getStatusIcon(ngo.approvedStatus || "pending")}
                          {(ngo.approvedStatus || "pending")
                            .charAt(0)
                            .toUpperCase() +
                            (ngo.approvedStatus || "pending").slice(1)}
                        </span>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4 text-gray-600 dark:text-gray-400">
                        <span className="text-sm">
                          {ngo.dateFounded
                            ? format(new Date(ngo.dateFounded), "MMM dd, yyyy")
                            : "N/A"}
                        </span>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4 text-right">
                        <button
                          onClick={() => handleViewOrganization(ngo._id)}
                          className="inline-flex items-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600 focus:outline-none dark:hover:bg-gray-700 dark:hover:text-gray-300"
                          title="View Details"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </div>

      {renderPagination()}
    </div>
  );
}
