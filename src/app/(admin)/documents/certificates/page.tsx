"use client";
import React, { useState, useEffect } from "react";
import {
  FaSearch,
  FaEye,
  FaFileDownload,
  FaFileAlt,
  FaFileContract,
  FaFileInvoice,
  FaFileMedical,
  FaFileSignature,
  FaFilter,
  FaTimes as XIcon,
  FaPlus,
  FaEllipsisV as FaEllipsisVertical,
} from "react-icons/fa";
import { format } from "date-fns";
import {
  getNgoCertificates,
  Certificate,
  CertificateFilter,
  CertificateStatus,
  CertificateType,
} from "../../../../services/certificates.services";

const statusStyles: Record<string, { bg: string; text: string; dot: string }> =
  {
    valid: {
      bg: "bg-green-100 dark:bg-green-900/30",
      text: "text-green-800 dark:text-green-200",
      dot: "bg-green-500",
    },
    expired: {
      bg: "bg-red-100 dark:bg-red-900/30",
      text: "text-red-800 dark:text-red-200",
      dot: "bg-red-500",
    },
    pending: {
      bg: "bg-yellow-100 dark:bg-yellow-900/30",
      text: "text-yellow-800 dark:text-yellow-200",
      dot: "bg-yellow-500",
    },
  };

const certificateTypeIcons: Record<string, React.ReactNode> = {
  "registration certificate": <FaFileAlt className="h-5 w-5 text-blue-500" />,
  "subscription certificate": (
    <FaFileInvoice className="h-5 w-5 text-blue-500" />
  ),
  "Operating License": <FaFileContract className="h-5 w-5 text-orange-500" />,
  "Compliance Certificate": (
    <FaFileMedical className="h-5 w-5 text-green-500" />
  ),
};

const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";
  return format(new Date(dateString), "MMM dd, yyyy");
};

const statusOptions: CertificateStatus[] = ["pending", "valid", "expired"];
const certificateTypeOptions: CertificateType[] = [
  "registration certificate",
  "subscription certificate",
];

export default function CertificatesPage() {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<CertificateFilter>({
    validityStatus: undefined,
    certificateType: undefined,
  });
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);

  useEffect(() => {
    const fetchCertificates = async () => {
      setLoading(true);
      setError(null);

      try {
        const token = localStorage.getItem("accessToken");
        const userStr = localStorage.getItem("user");
        const user = userStr ? JSON.parse(userStr) : null;
        const ngoId = user?.ngoId;

        console.log("=== REAL CERTIFICATES DEBUG ===");
        console.log("Token:", token ? "Present" : "Missing");
        console.log("User:", user);
        console.log("NGO ID:", ngoId);

        if (!token || !ngoId) {
          setError("Please log in as an NGO admin to view certificates");
          setLoading(false);
          return;
        }

        // Use NGO-specific certificate fetching
        const response = await getNgoCertificates(ngoId, filters, token);
        console.log("Certificates response:", response);
        setCertificates(response.data.certificates);
      } catch (err) {
        console.error("Error fetching certificates:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch certificates",
        );
      } finally {
        setLoading(false);
      }
    };

    fetchCertificates();
  }, [filters]);

  const toggleFilter = (filterType: keyof CertificateFilter, value: string) => {
    setFilters((prev) => {
      const currentFilter = prev[filterType];
      if (currentFilter === value) {
        return { ...prev, [filterType]: undefined };
      }
      return { ...prev, [filterType]: value };
    });
  };

  const clearFilters = () => {
    setFilters({
      validityStatus: undefined,
      certificateType: undefined,
    });
  };

  const filteredCertificates = certificates.filter((cert) => {
    const matchesSearch = Object.values(cert).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase()),
    );
    return matchesSearch;
  });

  const toggleMenu = (certId: string) => {
    setOpenMenuId(openMenuId === certId ? null : certId);
  };

  const handleView = (certId: string) => {
    const cert = certificates.find((c) => c.id === certId);
    setOpenMenuId(null);
    if (cert?.certificateUrl) {
      window.open(cert.certificateUrl, "_blank");
    } else {
      alert("Certificate file not available");
    }
  };

  const handleDownload = (certId: string) => {
    const cert = certificates.find((c) => c.id === certId);
    setOpenMenuId(null);
    if (cert?.certificateUrl) {
      const link = document.createElement("a");
      link.href = cert.certificateUrl;
      link.download = `certificate-${cert.certificateNumber}.pdf`;
      link.click();
    } else {
      alert("Certificate file not available");
    }
  };

  // Calculate statistics
  const totalCertificates = certificates.length;
  const validCertificates = certificates.filter(
    (cert) => cert.status === "valid",
  ).length;
  const expiringSoon = certificates.filter((cert) => {
    const expiryDate = new Date(cert.expiryDate);
    const threeMonthsFromNow = new Date();
    threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);
    return (
      expiryDate <= threeMonthsFromNow &&
      expiryDate >= new Date() &&
      cert.status === "valid"
    );
  }).length;

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-sm dark:border-gray-700 dark:bg-gray-800">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Certificate Management
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          View and manage all organization certificates
        </p>
      </div>

      {/* Stats Cards */}
      <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-3">
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center">
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900/30">
              <FaFileAlt className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Certificates
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {totalCertificates}
              </p>
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center">
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900/30">
              <FaFileSignature className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Valid Certificates
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {validCertificates}
              </p>
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center">
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900/30">
              <FaFileContract className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Expiring Soon
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {expiringSoon}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative flex-1">
          <FaSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search certificates..."
            className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          />
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            <FaFilter className="h-4 w-4" />
            Filters
            {(filters.validityStatus || filters.certificateType) && (
              <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {Object.values(filters).filter(Boolean).length}
              </span>
            )}
          </button>
          {/* <button className="flex items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <FaPlus className="h-4 w-4" />
            Add Certificate
          </button> */}
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <div className="flex gap-2">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
              >
                Clear all
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              >
                <XIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Certificate Type
              </h4>
              <div className="space-y-2">
                {certificateTypeOptions.map((type) => (
                  <div key={type} className="flex items-center">
                    <input
                      id={`type-${type}`}
                      type="checkbox"
                      checked={filters.certificateType === type}
                      onChange={() => toggleFilter("certificateType", type)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`type-${type}`}
                      className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300"
                    >
                      {type}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </h4>
              <div className="space-y-2">
                {statusOptions.map((status) => (
                  <div key={status} className="flex items-center">
                    <input
                      id={`status-${status}`}
                      type="checkbox"
                      checked={filters.validityStatus === status}
                      onChange={() => toggleFilter("validityStatus", status)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`status-${status}`}
                      className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300"
                    >
                      {status}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {(filters.validityStatus || filters.certificateType) && (
        <div className="mb-4 flex flex-wrap gap-2">
          {filters.certificateType && (
            <span className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium capitalize text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              Type: {filters.certificateType}
              <button
                onClick={() => toggleFilter("certificateType", "")}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-800 hover:bg-blue-200 dark:text-blue-200 dark:hover:bg-blue-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          )}
          {filters.validityStatus && (
            <span className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium capitalize text-green-800 dark:bg-green-900 dark:text-green-200">
              Status: {filters.validityStatus}
              <button
                onClick={() => toggleFilter("validityStatus", "")}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-800 hover:bg-green-200 dark:text-green-200 dark:hover:bg-green-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          )}
        </div>
      )}

      {/* Certificates Table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm dark:border-gray-700">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="py-12 text-center">Loading...</div>
          ) : error ? (
            <div className="py-12 text-center text-red-500">{error}</div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    Organization
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    Certificate Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    Issue Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    Expiry Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                {filteredCertificates.map((cert) => (
                  <tr
                    key={cert.id}
                    className="transition-colors hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {cert.organizationName}
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          {certificateTypeIcons[cert.certificateType] || (
                            <FaFileAlt className="h-5 w-5 text-gray-400" />
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm capitalize text-gray-900 dark:text-white">
                            {cert.certificateType}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(cert.issueDate)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(cert.expiryDate)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="flex items-center">
                        <span
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium capitalize ${statusStyles[cert.status]?.bg} ${statusStyles[cert.status]?.text}`}
                        >
                          <span
                            className={`mr-1.5 h-1.5 w-1.5 rounded-full ${statusStyles[cert.status]?.dot}`}
                          ></span>
                          {cert.status}
                        </span>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-right">
                      <div className="flex justify-end">
                        <div className="relative">
                          <button
                            onClick={() => toggleMenu(cert.id)}
                            className="inline-flex items-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:hover:bg-gray-700 dark:hover:text-gray-300"
                          >
                            <FaEllipsisVertical className="h-4 w-4" />
                          </button>

                          {/* Dropdown menu */}
                          {openMenuId === cert.id && (
                            <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700">
                              <button
                                onClick={() => handleView(cert.id)}
                                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                              >
                                <FaEye className="mr-3 h-4 w-4 text-gray-500" />
                                View
                              </button>
                              <button
                                onClick={() => handleDownload(cert.id)}
                                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                              >
                                <FaFileDownload className="mr-3 h-4 w-4 text-gray-500" />
                                Download
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        {!loading && !error && filteredCertificates.length === 0 && (
          <div className="px-6 py-12 text-center">
            <div className="flex flex-col items-center justify-center gap-2">
              <FaSearch className="h-8 w-8 text-gray-400" />
              <p className="text-lg font-medium">No certificates found</p>
              <p className="text-sm text-gray-500">
                Try adjusting your search or filters
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
