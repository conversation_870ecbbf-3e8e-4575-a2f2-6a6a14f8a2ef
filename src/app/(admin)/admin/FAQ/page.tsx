"use client";

import React, { useState, useEffect } from "react";
import { Modal } from "@/components/ui/modal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  getFaqs,
  createFaq,
  updateFaq,
  deleteFaq,
  Faq,
} from "@/services/faq.services";
import { Edit2, Trash2 } from "lucide-react";

export default function AdminFAQPage() {
  const [faqs, setFaqs] = useState<Faq[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingFaq, setEditingFaq] = useState<Faq | null>(null);
  const [editModalOpen, setEditModalOpen] = useState(false);

  const [modalOpen, setModalOpen] = useState(false);
  const [newFAQ, setNewFAQ] = useState({
    question: "",
    answer: "",
  });

  useEffect(() => {
    fetchFaqs();
  }, []);

  const fetchFaqs = async () => {
    try {
      setLoading(true);
      const response = await getFaqs();
      if (response.status === "success" && response.data) {
        setFaqs(response.data);
      } else {
        setError(response.message || "Failed to fetch FAQs");
      }
    } catch (err) {
      setError("An error occurred while fetching FAQs");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddFAQ = async () => {
    if (!newFAQ.question || !newFAQ.answer) return;

    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");
      if (!token) {
        setError("Authentication required");
        return;
      }

      const faqData = {
        question: newFAQ.question.trim(),
        answer: newFAQ.answer.trim(),
      };

      const response = await createFaq(faqData, token);

      if (response.status === "success" && response.data) {
        setFaqs([...faqs, response.data]);
        setNewFAQ({ question: "", answer: "" });
        setModalOpen(false);
      } else {
        alert("response: ");
        console.log(response.data);
        setError(response.message || "Failed to create FAQ");
      }
    } catch (err) {
      setError("An error occurred while creating the FAQ");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleEditFAQ = async () => {
    if (!editingFaq || !editingFaq._id) return;

    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");
      if (!token) {
        setError("Authentication required");
        return;
      }

      const response = await updateFaq(
        editingFaq._id,
        {
          question: editingFaq.question,
          answer: editingFaq.answer,
        },
        token,
      );

      if (response.status === "success" && response.data) {
        setFaqs(
          faqs.map((faq) =>
            faq._id === editingFaq._id ? response.data! : faq,
          ),
        );
        setEditingFaq(null);
        setEditModalOpen(false);
      } else {
        setError(response.message || "Failed to update FAQ");
      }
    } catch (err) {
      setError("An error occurred while updating the FAQ");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFAQ = async (id: string) => {
    if (!window.confirm("Are you sure you want to delete this FAQ?")) return;

    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");
      if (!token) {
        setError("Authentication required");
        return;
      }

      const response = await deleteFaq(id, token);

      if (response.status === "success") {
        setFaqs(faqs.filter((faq) => faq._id !== id));
      } else {
        setError(response.message || "Failed to delete FAQ");
      }
    } catch (err) {
      setError("An error occurred while deleting the FAQ");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Error Message */}
      {error && (
        <div className="mb-6 rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
          {error}
          <button
            onClick={() => setError(null)}
            className="ml-2 text-sm underline hover:no-underline"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Frequently Asked Questions
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage FAQs for the platform ({faqs.length} total)
          </p>
        </div>
        <Button
          className="text-white"
          onClick={() => setModalOpen(true)}
          disabled={loading}
        >
          + New FAQ
        </Button>
      </div>

      {/* FAQ List */}
      <div className="space-y-4">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">
              Loading FAQs...
            </span>
          </div>
        ) : faqs.length > 0 ? (
          faqs.map((item) => (
            <div
              key={item._id}
              className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800"
            >
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {item.question}
                </h3>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => {
                      setEditingFaq(item);
                      setEditModalOpen(true);
                    }}
                    className="flex items-center rounded-md px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                  >
                    <Edit2 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteFAQ(item._id!)}
                    className="flex items-center rounded-md px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-800/20"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <p className="mt-3 leading-relaxed text-gray-600 dark:text-gray-400">
                {item.answer}
              </p>
            </div>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center py-16">
            <p className="mt-2 text-lg font-medium text-gray-700 dark:text-gray-300">
              No FAQs found
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Create your first FAQ to get started
            </p>
          </div>
        )}
      </div>

      {/* Modal for adding new FAQ */}
      <Modal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        title="Add New FAQ"
      >
        <div className="space-y-4">
          <Input
            placeholder="Question"
            value={newFAQ.question}
            onChange={(e) => setNewFAQ({ ...newFAQ, question: e.target.value })}
          />
          <textarea
            name="answer"
            value={newFAQ.answer}
            onChange={(e) => setNewFAQ({ ...newFAQ, answer: e.target.value })}
            placeholder="Answer"
            className="w-full rounded-md border border-gray-300 p-3 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            rows={4}
          />
        </div>

        <div className="mt-6 flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => setModalOpen(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button onClick={handleAddFAQ} disabled={loading}>
            {loading ? "Adding..." : "Add FAQ"}
          </Button>
        </div>
      </Modal>

      {/* Modal for editing FAQ */}
      <Modal
        isOpen={editModalOpen}
        onClose={() => {
          setEditModalOpen(false);
          setEditingFaq(null);
        }}
        title="Edit FAQ"
      >
        {editingFaq && (
          <div className="space-y-4">
            <Input
              placeholder="Question"
              value={editingFaq.question}
              onChange={(e) =>
                setEditingFaq({ ...editingFaq, question: e.target.value })
              }
            />
            <textarea
              name="answer"
              value={editingFaq.answer}
              onChange={(e) =>
                setEditingFaq({ ...editingFaq, answer: e.target.value })
              }
              placeholder="Answer"
              className="w-full rounded-md border border-gray-300 p-3 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              rows={4}
            />
            <div className="mt-6 flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setEditModalOpen(false);
                  setEditingFaq(null);
                }}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button onClick={handleEditFAQ} disabled={loading}>
                {loading ? "Updating..." : "Update FAQ"}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}
