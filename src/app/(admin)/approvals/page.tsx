"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import ApprovalModal from "@/components/approvalModal";
import { getNGOsByApprovementStage, INgo } from "@/services/ngo.services";
import {
  SearchIcon,
  FilterIcon,
  XIcon,
  Download,
  EyeIcon,
  CheckCircle,
  XCircle,
  Loader2,
} from "lucide-react";
import { format } from "date-fns";
import {
  HiOutlineBuildingOffice,
  HiOutlineCalendar,
  HiOutlineClock,
  HiOutlineDocumentText,
  HiOutlineExclamationTriangle,
} from "react-icons/hi2";

type Payment = {
  _id: string;
  ngoName: string;
  contactPerson: string;
  contactEmail: string;
  processingFeeUrl: string;
  registrationFeeUrl: string;
  ngoType: string;
  status: string;
  createdAt: string;
};

function NgoToPayment(ngo: INgo[]): Payment[] {
  return ngo.map((ngo) => ({
    _id: ngo._id,
    ngoName: ngo.name,
    contactPerson: ngo.chairpersonName || ngo.chiefExecutiveName || "N/A",
    contactEmail: ngo.chairpersonEmail || ngo.chiefExecutiveEmail || "N/A",
    processingFeeUrl: ngo.processingFeeUrl || "",
    registrationFeeUrl: ngo.registrationFeeUrl || "",
    ngoType: ngo.type,
    status: ngo.approvementStage || ngo.approvedStatus || "pending",
    createdAt: ngo.createdAt || "",
  }));
}

// Dummy data generator
const generateDummyPayments = (): Payment[] => {
  const statuses = [
    "financial",
    "documentation",
    "completed",
    "documents-declined",
    "payment-declined",
  ];
  const ngoTypes = ["local", "international"];
  const names = [
    "Hope Foundation Malawi",
    "Green Earth Initiative",
    "Youth Empowerment Trust",
    "Health Access Network",
    "Education For All",
    "Women's Rights Coalition",
    "Clean Water Project",
    "Agricultural Development Fund",
    "Child Protection Agency",
    "Community Health Partners",
  ];

  return Array.from({ length: 15 }, (_, i) => ({
    _id: `ngo-${i + 1}`,
    ngoName: names[i % names.length] + (i > 9 ? ` ${Math.floor(i / 10)}` : ""),
    contactPerson: `Contact Person ${i + 1}`,
    contactEmail: `contact${i + 1}@example.com`,
    processingFeeUrl: "https://example.com/processing-fee.pdf",
    registrationFeeUrl: "https://example.com/registration-fee.pdf",
    ngoType: ngoTypes[i % 2],
    status: statuses[i % statuses.length],
    createdAt: new Date(
      Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
    ).toISOString(),
  }));
};

export default function RegistrationPayments() {
  const [searchQuery, setSearchQuery] = useState("");
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: [] as string[],
    ngoType: [] as string[],
  });
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const role = user?.role?.name || "";

  let stage = "";
  if (role === "staff_admin" || role === "staff_registry") {
    stage = "documentation";
  } else if (role === "finance_officer") {
    stage = "financial";
  }

  const sectionTitle =
    role === "staff_admin" || role === "staff_registry"
      ? "Pending Document Approvals"
      : "Pending Proof of Payment Uploads";

  const sectionDescription =
    role === "staff_admin" || role === "staff_registry"
      ? "Track and manage pending document approvals from NGOs"
      : "Track and manage pending payment proof uploads from NGOs";

  const approvedLabel =
    role === "staff_admin" || role === "staff_registry"
      ? "Approved Documents"
      : "Approved Payments";

  const declinedLabel =
    role === "staff_admin" || role === "staff_registry"
      ? "Declined Documents"
      : "Declined Payments";

  const approvedStatuses =
    role === "staff_admin" || role === "staff_registry"
      ? ["completed"]
      : ["documentation", "completed"];

  const statusOptions = [
    "financial",
    "documentation",
    "completed",
    "documents-declined",
    "payment-declined",
  ];
  const ngoTypeOptions = ["local", "international"];

  const fetchPayments = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");
      if (token && stage) {
        const response = await getNGOsByApprovementStage(stage, token);

        if (response.status === "success") {
          setPayments(NgoToPayment(response.data));
        } else {
          setError(response.message);
        }
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  /*
  const fetchPayments = async () => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Generate dummy data
      const dummyData = generateDummyPayments();
      setPayments(dummyData);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };*/

  useEffect(() => {
    fetchPayments();
  }, []);

  // Debounced search effect
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (currentPage !== 1) {
        setCurrentPage(1);
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery]);

  const toggleFilter = (filterType: keyof typeof filters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: [],
      ngoType: [],
    });
    setCurrentPage(1);
  };

  // Filter payments based on search and filters
  const filtered = payments.filter((payment) => {
    const matchesSearch =
      searchQuery === "" ||
      payment.ngoName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.contactPerson.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.contactEmail.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      filters.status.length === 0 || filters.status.includes(payment.status);

    const matchesType =
      filters.ngoType.length === 0 || filters.ngoType.includes(payment.ngoType);

    return matchesSearch && matchesStatus && matchesType;
  });

  // Pagination
  const totalPages = Math.ceil(filtered.length / pageSize);
  const paginatedData = filtered.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize,
  );

  const filterPaymentsByStatus = (payments: Payment[], statuses: string[]) => {
    return payments.filter((p) => statuses.includes(p.status));
  };

  const filterPaymentsByStatusAndDays = (
    payments: Payment[],
    statuses: string[],
    days: number,
  ) => {
    const cutoff = Date.now() - days * 24 * 60 * 60 * 1000;
    return payments.filter(
      (p) =>
        statuses.includes(p.status) && new Date(p.createdAt).getTime() < cutoff,
    );
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "financial":
      case "documentation":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "documents-declined":
      case "payment-declined":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy");
  };

  const getDaysSince = (dateString: string) => {
    return Math.floor(
      (Date.now() - new Date(dateString).getTime()) / (1000 * 60 * 60 * 24),
    );
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    const startPage = Math.max(
      1,
      currentPage - Math.floor(maxVisiblePages / 2),
    );
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="mt-6 flex items-center justify-between">
        <div className="text-sm text-gray-700 dark:text-gray-300">
          Showing {(currentPage - 1) * pageSize + 1} to{" "}
          {Math.min(currentPage * pageSize, filtered.length)} of{" "}
          {filtered.length} results
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            Previous
          </button>
          {pages.map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`rounded-md px-3 py-2 text-sm font-medium ${
                page === currentPage
                  ? "bg-blue-600 text-white"
                  : "border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
              }`}
            >
              {page}
            </button>
          ))}
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            Next
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      {error && (
        <div className="mb-6 rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900 dark:text-red-200">
          <div className="flex items-center gap-2">
            <span>Error: {error}</span>
            <button
              onClick={fetchPayments}
              className="ml-auto text-sm underline hover:no-underline"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className="mb-8 flex flex-col justify-between gap-6 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {sectionTitle}
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            {sectionDescription} ({filtered.length} total)
          </p>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search organizations..."
              className="w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <FilterIcon className="h-4 w-4" />
              Filters
              {Object.values(filters).some((arr) => arr.length > 0) && (
                <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {Object.values(filters).flat().length}
                </span>
              )}
            </button>

            <button
              onClick={() => {}}
              className="flex items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <Download className="h-4 w-4" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="mb-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-xl bg-gradient-to-br from-yellow-50 to-yellow-100 p-6 shadow-sm dark:from-yellow-900/20 dark:to-yellow-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending Approval
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {filterPaymentsByStatus(payments, [stage]).length}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
              <HiOutlineDocumentText className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-gradient-to-br from-green-50 to-green-100 p-6 shadow-sm dark:from-green-900/20 dark:to-green-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {approvedLabel}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {filterPaymentsByStatus(payments, approvedStatuses).length}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-gradient-to-br from-red-50 to-red-100 p-6 shadow-sm dark:from-red-900/20 dark:to-red-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Overdue Approval
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {filterPaymentsByStatusAndDays(payments, [stage], 2).length}
              </p>
            </div>
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900">
              <HiOutlineExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-gradient-to-br from-blue-50 to-blue-100 p-6 shadow-sm dark:from-blue-900/20 dark:to-blue-800/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {declinedLabel}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {
                  filterPaymentsByStatus(payments, [
                    "documents-declined",
                    "payment-declined",
                  ]).length
                }
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <XCircle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>
      </div>

      {showFilters && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <div className="flex gap-2">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <XIcon className="h-4 w-4" />
                Clear all
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              >
                <XIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </h4>
              <div className="space-y-2">
                {statusOptions.map((status) => (
                  <div key={status} className="flex items-center">
                    <input
                      id={`status-${status}`}
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={() => toggleFilter("status", status)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`status-${status}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {status.charAt(0).toUpperCase() +
                        status.slice(1).replace("-", " ")}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                NGO Type
              </h4>
              <div className="space-y-2">
                {ngoTypeOptions.map((type) => (
                  <div key={type} className="flex items-center">
                    <input
                      id={`type-${type}`}
                      type="checkbox"
                      checked={filters.ngoType.includes(type)}
                      onChange={() => toggleFilter("ngoType", type)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`type-${type}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {Object.values(filters).some((arr) => arr.length > 0) && (
        <div className="mb-4 flex flex-wrap gap-2">
          {filters.status.map((status) => (
            <span
              key={`status-${status}`}
              className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              Status: {status.charAt(0).toUpperCase() + status.slice(1)}
              <button
                onClick={() => toggleFilter("status", status)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-800 hover:bg-blue-200 dark:text-blue-200 dark:hover:bg-blue-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
          {filters.ngoType.map((type) => (
            <span
              key={`type-${type}`}
              className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              Type: {type.charAt(0).toUpperCase() + type.slice(1)}
              <button
                onClick={() => toggleFilter("ngoType", type)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-800 hover:bg-green-200 dark:text-green-200 dark:hover:bg-green-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm dark:border-gray-700">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                Loading approvals...
              </span>
            </div>
          ) : (
            <Table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <TableHeader>
                <TableRow className="bg-gray-50 dark:bg-gray-800">
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    NGO Details
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Type
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Registration Date
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Days Pending
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Status
                  </TableHead>
                  <TableHead className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                {paginatedData.map((payment) => (
                  <TableRow
                    key={payment._id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                  >
                    <TableCell className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <HiOutlineBuildingOffice className="h-10 w-10 text-gray-400" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {payment.ngoName}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {payment.contactPerson}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {payment.contactEmail}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4">
                      <span className="inline-flex items-center rounded-full border border-gray-300 bg-gray-50 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300">
                        {payment.ngoType.charAt(0).toUpperCase() +
                          payment.ngoType.slice(1)}
                      </span>
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4 text-gray-600 dark:text-gray-400">
                      <div className="flex items-center space-x-2">
                        <HiOutlineCalendar className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">
                          {formatDate(payment.createdAt)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <HiOutlineClock className="h-4 w-4 text-gray-400" />
                        <span
                          className={`text-sm font-medium ${getDaysSince(payment.createdAt) > 2 ? "text-red-600 dark:text-red-400" : "text-gray-900 dark:text-white"}`}
                        >
                          {getDaysSince(payment.createdAt)} days
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4">
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(payment.status)}`}
                      >
                        {payment.status.charAt(0).toUpperCase() +
                          payment.status.slice(1).replace("-", " ")}
                      </span>
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4 text-right">
                      <button
                        onClick={() => setSelectedPayment(payment)}
                        className="inline-flex items-center rounded-md bg-blue-100 p-2 text-blue-600 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-blue-900 dark:text-blue-400 dark:hover:bg-blue-800"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                    </TableCell>
                  </TableRow>
                ))}
                {paginatedData.length === 0 && !loading && (
                  <TableRow>
                    <TableCell
                      colSpan={6}
                      className="py-12 text-center text-gray-500"
                    >
                      <div className="flex flex-col items-center justify-center gap-2">
                        <SearchIcon className="h-8 w-8 text-gray-400" />
                        <p className="text-lg font-medium">
                          No approvals found
                        </p>
                        <p className="text-sm text-gray-500">
                          Try adjusting your search or filters
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </div>

      {renderPagination()}

      {selectedPayment && (
        <ApprovalModal
          data={selectedPayment}
          onClose={() => setSelectedPayment(null)}
        />
      )}
    </div>
  );
}
