"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  ChevronDown,
  Eye,
  Download,
  MoreHorizontal,
} from "lucide-react";
import { motion } from "framer-motion";
import { formatToMWK } from "@/lib/format-number";
import { RecentPayment } from "@/types/finance-officer-statistics.types";
import { getFinanceOfficerStatistics } from "@/services/statistics.services";

export function FinancialTables() {
  const [searchQuery, setSearchQuery] = useState("");
  const [payments, setPayments] = useState<RecentPayment[]>([]);
  const token = localStorage.getItem("accessToken")!;

  useEffect(() => {
    const fetchPayments = async () => {
      const stats = await getFinanceOfficerStatistics(token);
      if (stats.data && stats.data.recentPayments) {
        setPayments(stats.data.recentPayments);
      }
    };

    fetchPayments();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
      case "completed":
        return "bg-success/10 text-success";
      case "pending":
        return "bg-warning/10 text-warning";
      case "overdue":
      case "failed":
        return "bg-danger/10 text-danger";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  const filteredPayments = payments.filter((payment) =>
    payment.ngoName.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-dark dark:text-white">
            Financial Overview
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor invoices, payments, and financial activities
          </p>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full rounded-lg border border-gray-300 bg-white px-10 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
          <button
            onClick={() =>
              alert("Filter functionality will be implemented soon")
            }
            className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:hover:bg-gray-700"
          >
            <Filter className="h-4 w-4" />
            Filter
            <ChevronDown className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 dark:border-gray-700">
        <button
          className={`border-b-2 border-primary px-4 py-2 text-sm font-medium text-primary transition-colors`}
        >
          Recent Payments ({filteredPayments.length})
        </button>
      </div>

      {/* Table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    NGO Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Receipt #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Actions
                  </th>
                </>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {filteredPayments.map((payment) => (
                <tr
                  key={payment.receiptNumber}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td className="px-6 py-4 text-sm font-medium text-dark dark:text-white">
                    {payment.ngoName}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                    {payment.receiptNumber}
                  </td>
                  <td className="px-6 py-4 text-sm font-medium text-dark dark:text-white">
                    {formatToMWK(payment.amount)}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                    {new Date(payment.date).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() =>
                          alert(
                            `View payment details for ${payment.organization}`,
                          )
                        }
                        className="rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4 text-gray-500" />
                      </button>
                      <button
                        onClick={() =>
                          alert(`Download receipt for ${payment.organization}`)
                        }
                        className="rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
                        title="Download Receipt"
                      >
                        <Download className="h-4 w-4 text-gray-500" />
                      </button>
                      <button
                        onClick={() => alert("More options coming soon")}
                        className="rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
                        title="More Options"
                      >
                        <MoreHorizontal className="h-4 w-4 text-gray-500" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </motion.div>
  );
}
