"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  HiOutlineDocumentText,
  HiOutlineUserGroup,
  HiOutlineFolderOpen,
  HiOutlineCalendar,
  HiOutlineBell,
  HiOutlineExclamationTriangle,
  HiOutlineCheckCircle,
  HiOutlineClock,
  HiOutlineCurrencyDollar,
  HiOutlineMapPin,
  HiOutlineChartBar,
} from "react-icons/hi2";

interface DashboardWidgetsProps {
  className?: string;
}

export function NGODashboardWidgets({ className }: DashboardWidgetsProps) {
  const router = useRouter();
  const [widgetData, setWidgetData] = useState({
    recentActivities: [
      {
        id: "1",
        type: "document_upload",
        title: "Annual Report Uploaded",
        description: "Annual report for 2024 has been successfully uploaded",
        date: "2024-10-20",
        status: "completed",
      },
      {
        id: "2",
        type: "member_joined",
        title: "New Member Added",
        description: "<PERSON> has been added as Finance Officer",
        date: "2024-10-19",
        status: "pending",
      },
      {
        id: "3",
        type: "project_update",
        title: "Project Progress Updated",
        description: "Community Education Initiative progress updated to 75%",
        date: "2024-10-18",
        status: "completed",
      },
      {
        id: "4",
        type: "payment_received",
        title: "Payment Received",
        description: "Processing fee payment of MK 50,000 received",
        date: "2024-10-17",
        status: "completed",
      },
    ],
    upcomingDeadlines: [
      {
        id: "1",
        title: "Membership Renewal",
        date: "2024-12-31",
        type: "renewal",
        priority: "high",
      },
      {
        id: "2",
        title: "Annual Report Submission",
        date: "2024-11-15",
        type: "document",
        priority: "medium",
      },
      {
        id: "3",
        title: "Board Meeting",
        date: "2024-10-25",
        type: "meeting",
        priority: "low",
      },
    ],
    quickStats: {
      totalMembers: 8,
      activeProjects: 5,
      pendingDocuments: 2,
      upcomingEvents: 3,
    },
  });

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "document_upload":
        return <HiOutlineDocumentText className="h-5 w-5 text-blue-600" />;
      case "member_joined":
        return <HiOutlineUserGroup className="h-5 w-5 text-green-600" />;
      case "project_update":
        return <HiOutlineFolderOpen className="h-5 w-5 text-purple-600" />;
      case "payment_received":
        return <HiOutlineCurrencyDollar className="h-5 w-5 text-green-600" />;
      default:
        return <HiOutlineBell className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "pending":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      case "medium":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "low":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Quick Stats */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Members
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {widgetData.quickStats.totalMembers}
              </p>
            </div>
            <HiOutlineUserGroup className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Projects
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {widgetData.quickStats.activeProjects}
              </p>
            </div>
            <HiOutlineFolderOpen className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending Documents
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {widgetData.quickStats.pendingDocuments}
              </p>
            </div>
            <HiOutlineDocumentText className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Upcoming Events
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {widgetData.quickStats.upcomingEvents}
              </p>
            </div>
            <HiOutlineCalendar className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Recent Activities and Upcoming Deadlines */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Recent Activities */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineBell className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Activities
            </h3>
          </div>

          <div className="space-y-4">
            {widgetData.recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {activity.title}
                    </h4>
                    <span
                      className={`rounded-full px-2 py-1 text-xs ${getStatusColor(activity.status)}`}
                    >
                      {activity.status}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    {activity.description}
                  </p>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    {activity.date}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Upcoming Deadlines */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineExclamationTriangle className="h-5 w-5 text-orange-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Upcoming Deadlines
            </h3>
          </div>

          <div className="space-y-4">
            {widgetData.upcomingDeadlines.map((deadline) => (
              <div key={deadline.id} className="flex items-start gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
                  <HiOutlineCalendar className="h-4 w-4 text-orange-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {deadline.title}
                    </h4>
                    <span
                      className={`rounded-full px-2 py-1 text-xs ${getPriorityColor(deadline.priority)}`}
                    >
                      {deadline.priority}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Due: {deadline.date}
                  </p>
                  <p className="mt-1 text-xs capitalize text-gray-500 dark:text-gray-400">
                    {deadline.type}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center gap-2">
          <HiOutlineChartBar className="h-5 w-5 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Quick Actions
          </h3>
        </div>

        <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-4">
          <button
            onClick={() => router.push("/organization/documents")}
            className="flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-3 text-white transition hover:bg-blue-700"
          >
            <HiOutlineDocumentText className="h-4 w-4" />
            Upload Document
          </button>
          <button
            onClick={() => router.push("/organization/members")}
            className="flex items-center gap-2 rounded-lg bg-green-600 px-4 py-3 text-white transition hover:bg-green-700"
          >
            <HiOutlineUserGroup className="h-4 w-4" />
            Add Member
          </button>
          <button
            onClick={() => router.push("/organization/projects")}
            className="flex items-center gap-2 rounded-lg bg-purple-600 px-4 py-3 text-white transition hover:bg-purple-700"
          >
            <HiOutlineFolderOpen className="h-4 w-4" />
            Create Project
          </button>
          <button
            onClick={() =>
              alert("Meeting scheduling functionality will be implemented soon")
            }
            className="flex items-center gap-2 rounded-lg bg-orange-600 px-4 py-3 text-white transition hover:bg-orange-700"
          >
            <HiOutlineCalendar className="h-4 w-4" />
            Schedule Meeting
          </button>
        </div>
      </div>
    </div>
  );
}
