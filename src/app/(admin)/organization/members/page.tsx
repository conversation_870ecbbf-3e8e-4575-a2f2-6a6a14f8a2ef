"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineUserPlus,
  HiOutlineUser,
  HiOutlineEnvelope,
  HiOutlinePhone,
  HiOutlineCalendar,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineEye,
  HiOutlinePlus,
} from "react-icons/hi2";
import { FaTimes as XIcon } from "react-icons/fa";
import { format } from "date-fns";
import {
  User,
  createNgoMember,
  getNgoMembers,
  removeNgoMember,
} from "@/services/users.services";

export default function OrganizationMembersPage() {
  const token = localStorage.getItem("accessToken") || "";
  const [members, setMembers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });
  const [showInviteMemberModal, setShowInviteMemberModal] = useState(false);
  const [newMemberData, setNewMemberData] = useState({
    fullname: "",
    email: "",
  });
  const [isInviting, setIsInviting] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<User | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchMembers = async () => {
    try {
      setIsLoading(true);
      const response = await getNgoMembers(
        {
          page: pagination.page,
          limit: pagination.limit,
        },
        token,
      );

      if (response.status === "success") {
        setMembers(response.data);
        setPagination(response.pagination);
      } else {
        setError("Failed to fetch members");
      }
    } catch (err) {
      setError("Error fetching members");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMembers();
  }, [pagination.page]);

  const handleInviteMember = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsInviting(true);

    try {
      const response = await createNgoMember(newMemberData, token);
      if (response.status === "success") {
        setShowInviteMemberModal(false);
        setNewMemberData({ fullname: "", email: "" });
        fetchMembers();
        alert(
          "Member invited successfully! They will receive an email invitation.",
        );
      } else {
        alert(response.message || "Failed to invite member");
      }
    } catch (err: any) {
      alert(err.message || "Error inviting member");
    } finally {
      setIsInviting(false);
    }
  };

  const handleRemoveMember = async () => {
    if (!memberToDelete) return;
    setIsDeleting(true);

    try {
      const response = await removeNgoMember(memberToDelete._id, token);
      if (response.status === "success") {
        setShowDeleteModal(false);
        setMemberToDelete(null);
        fetchMembers();
        alert("Member removed successfully");
      } else {
        alert(response.message || "Failed to remove member");
      }
    } catch (err: any) {
      alert(err.message || "Error removing member");
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "pending_organization":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "pending":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "inactive":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <HiOutlineCheckCircle className="h-4 w-4 text-green-600" />;
      case "pending_organization":
        return <HiOutlineClock className="h-4 w-4 text-yellow-600" />;
      case "pending":
        return <HiOutlineClock className="h-4 w-4 text-yellow-600" />;
      case "inactive":
        return <HiOutlineXCircle className="h-4 w-4 text-red-600" />;
      default:
        return <HiOutlineClock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Active";
      case "pending_organization":
        return "Pending";
      case "inactive":
        return "Inactive";
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Organization Members
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your organization&apos;s members and their roles
          </p>
        </div>
        <button
          onClick={() => setShowInviteMemberModal(true)}
          className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/80"
        >
          <HiOutlinePlus className="h-4 w-4" />
          Add Member
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Members
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {members.length}
              </p>
            </div>
            <HiOutlineUser className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Members
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {members.filter((m) => m.status === "active").length}
              </p>
            </div>
            <HiOutlineCheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending Invites
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {
                  members.filter((m) => m.status === "pending_organization")
                    .length
                }
              </p>
            </div>
            <HiOutlineClock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Email Verified
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {members.filter((m) => m.emailVerified).length}
              </p>
            </div>
            <HiOutlineCheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Members Table */}
      <div className="rounded-lg bg-white shadow-sm dark:bg-gray-800">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Member Directory
          </h2>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Member
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Role
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Join Date
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {members.map((member) => (
                  <tr
                    key={member._id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
                          <HiOutlineUser className="h-5 w-5 text-primary" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {member.fullname}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {member.email}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {member.role?.name || "Member"}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(member.status)}
                        <span
                          className={`rounded-full px-2 py-1 text-xs ${getStatusColor(member.status)}`}
                        >
                          {getStatusText(member.status)}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {format(new Date(member.createdAt), "MMM dd, yyyy")}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {member.emailVerified ? (
                          <span className="text-green-600 dark:text-green-400">
                            ✓ Verified
                          </span>
                        ) : (
                          <span className="text-yellow-600 dark:text-yellow-400">
                            ⚠ Pending
                          </span>
                        )}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlineEye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setMemberToDelete(member);
                            setShowDeleteModal(true);
                          }}
                          className="rounded p-1 text-red-400 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-300"
                        >
                          <HiOutlineTrash className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-red-700 dark:border-red-800 dark:bg-red-900/30 dark:text-red-200">
          {error}
        </div>
      )}

      {/* Invite Member Modal */}
      {showInviteMemberModal && (
        <div className="fixed inset-0 z-50 h-full w-full overflow-y-auto bg-gray-600 bg-opacity-50">
          <div className="relative top-20 mx-auto w-96 rounded-md border bg-white p-5 shadow-lg dark:bg-gray-800">
            <div className="mt-3">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Invite New Member
                </h3>
                <button
                  onClick={() => setShowInviteMemberModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <XIcon className="h-6 w-6" />
                </button>
              </div>
              <form onSubmit={handleInviteMember} className="space-y-4">
                <div>
                  <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Full Name
                  </label>
                  <input
                    type="text"
                    required
                    value={newMemberData.fullname}
                    onChange={(e) =>
                      setNewMemberData((prev) => ({
                        ...prev,
                        fullname: e.target.value,
                      }))
                    }
                    className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter member's full name"
                  />
                </div>
                <div>
                  <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email Address
                  </label>
                  <input
                    type="email"
                    required
                    value={newMemberData.email}
                    onChange={(e) =>
                      setNewMemberData((prev) => ({
                        ...prev,
                        email: e.target.value,
                      }))
                    }
                    className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter member's email address"
                  />
                </div>
                <div className="flex items-center justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowInviteMemberModal(false)}
                    className="rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isInviting}
                    className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isInviting ? (
                      <>
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <HiOutlineUserPlus className="mr-2" />
                        Send Invitation
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && memberToDelete && (
        <div className="fixed inset-0 z-50 h-full w-full overflow-y-auto bg-gray-600 bg-opacity-50">
          <div className="relative top-20 mx-auto w-96 rounded-md border bg-white p-5 shadow-lg dark:bg-gray-800">
            <div className="mt-3">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Remove Member
                </h3>
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <XIcon className="h-6 w-6" />
                </button>
              </div>
              <div className="mb-4">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Are you sure you want to remove{" "}
                  <span className="font-medium text-gray-900 dark:text-white">
                    {memberToDelete.fullname}
                  </span>{" "}
                  from your organization? This action cannot be undone.
                </p>
              </div>
              <div className="flex items-center justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRemoveMember}
                  disabled={isDeleting}
                  className="flex items-center rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50"
                >
                  {isDeleting ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                      Removing...
                    </>
                  ) : (
                    <>
                      <HiOutlineTrash className="mr-2" />
                      Remove Member
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
