"use client";

import { useState, useEffect } from "react";
import { ProjectIcon } from "@/components/Layouts/sidebar/icons";
import { getCurrentUser } from "@/utils/auth.utils";
import { User } from "@/services/auth.services";
import {
  getAllNgoProjects,
  createNgoProject,
  updateNgoProject,
  deleteNgoProject,
  INgoProject,
} from "@/services/projects";

interface Project {
  id: string;
  name: string;
  description: string;
  status: "upcoming" | "in progress" | "completed";
  progress: number;
  startDate: string;
  endDate: string;
  budget: number;
  organization: string;
  sector: string;
  district: string;
}

// Calculate project status based on dates
const calculateProjectStatus = (
  startDate: string,
  endDate: string,
): "upcoming" | "in progress" | "completed" => {
  const now = new Date();
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Set time to start of day for accurate comparison
  now.setHours(0, 0, 0, 0);
  start.setHours(0, 0, 0, 0);
  end.setHours(0, 0, 0, 0);

  if (now < start) return "upcoming";
  if (now >= start && now <= end) return "in progress";
  return "completed";
};

// Function to convert API project format to our internal format
const mapApiToProject = (ngoProject: INgoProject): Project => {
  const startDate =
    ngoProject.startDate || new Date().toISOString().split("T")[0];
  const endDate =
    ngoProject.endDate ||
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0];

  return {
    id: ngoProject._id,
    name: ngoProject.description.split(" ").slice(0, 3).join(" "), // Using first few words as name
    description: ngoProject.description,
    status: calculateProjectStatus(startDate, endDate), // Calculate status from dates
    progress: 0, // Progress not supported by backend model
    startDate,
    endDate,
    budget: ngoProject.budget || 0,
    organization: ngoProject.ngo || "Unknown",
    sector: "General", // This field doesn't exist in API model
    district: ngoProject.district || "Unknown",
  };
};

// Function to convert our project format to API format
const mapProjectToApi = (project: Project): Partial<INgoProject> => {
  return {
    description: project.description,
    budget: project.budget,
    district: project.district,
    startDate: project.startDate,
    endDate: project.endDate,
    status: project.status,
  };
};

const statusColors = {
  upcoming:
    "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:bg-opacity-30 dark:text-blue-400",
  "in progress":
    "bg-green-100 text-green-700 dark:bg-green-900 dark:bg-opacity-30 dark:text-green-400",
  completed:
    "bg-gray-100 text-gray-700 dark:bg-gray-900 dark:bg-opacity-30 dark:text-gray-400",
};

const statusLabels = {
  upcoming: "Upcoming",
  "in progress": "In Progress",
  completed: "Completed",
};

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sectorFilter, setSectorFilter] = useState<string>("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [newProject, setNewProject] = useState({
    title: "",
    description: "",
    url: "",
    budget: 0,
    district: "",
    startDate: new Date().toISOString().split("T")[0],
    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
  });

  // Initialize user and fetch projects on component mount
  useEffect(() => {
    const initializeData = async () => {
      try {
        setIsLoading(true);
        const currentUser = await getCurrentUser();
        const accessToken = localStorage.getItem("accessToken");

        console.log("=== PROJECTS DEBUG ===");
        console.log("Current user:", currentUser);
        console.log("User ngoId:", currentUser?.ngoId);
        console.log("Access token present:", !!accessToken);

        setUser(currentUser);
        setToken(accessToken);

        if (currentUser?.ngoId && accessToken) {
          await fetchProjects(accessToken);
        } else {
          console.error("Missing required data:", {
            hasUser: !!currentUser,
            hasNgoId: !!currentUser?.ngoId,
            hasToken: !!accessToken,
          });
          setError("Authentication required");
          setIsLoading(false);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to initialize");
        console.error("Error initializing:", err);
        setIsLoading(false);
      }
    };

    initializeData();
  }, []);

  const fetchProjects = async (accessToken: string) => {
    try {
      const response = await getAllNgoProjects(accessToken);

      if (Array.isArray(response)) {
        setProjects(response.map(mapApiToProject));
      } else {
        setProjects([]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch projects");
      console.error("Error fetching projects:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredProjects = projects.filter((project) => {
    const matchesSearch =
      project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.organization.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || project.status === statusFilter;
    const matchesSector =
      sectorFilter === "all" || project.sector === sectorFilter;

    return matchesSearch && matchesStatus && matchesSector;
  });

  const sectors = [...new Set(projects.map((p) => p.sector))];
  const totalBudget = projects.reduce((sum, p) => sum + p.budget, 0);
  const upcomingProjects = projects.filter(
    (p) => p.status === "upcoming",
  ).length;
  const inProgressProjects = projects.filter(
    (p) => p.status === "in progress",
  ).length;
  const completedProjects = projects.filter(
    (p) => p.status === "completed",
  ).length;

  const handleAddProject = async () => {
    if (newProject.title && newProject.description) {
      try {
        if (!user?.ngoId || !token) {
          setError("Authentication required");
          return;
        }

        const formData = new FormData();
        formData.append("name", newProject.title);
        formData.append("description", newProject.description);
        formData.append("budget", newProject.budget.toString());
        formData.append("startDate", newProject.startDate);
        formData.append("endDate", newProject.endDate);
        formData.append("district", newProject.district);
        formData.append("ngo", user.ngoId);
        if (newProject.url) {
          formData.append("urls", JSON.stringify([newProject.url]));
        }

        const response = await createNgoProject(formData, token);

        // Add the new project to the state and recalculate all statuses
        const updatedProjects = [...projects, mapApiToProject(response)];
        // Recalculate statuses for all projects in case dates have changed
        const refreshedProjects = updatedProjects.map((project) => ({
          ...project,
          status: calculateProjectStatus(project.startDate, project.endDate),
        }));
        setProjects(refreshedProjects);
        setNewProject({
          title: "",
          description: "",
          url: "",
          budget: 0,
          district: "",
          startDate: new Date().toISOString().split("T")[0],
          endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0],
        });
        setIsModalOpen(false);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to create project",
        );
        console.error("Error creating project:", err);
      }
    }
  };

  const handleCancel = () => {
    setNewProject({
      title: "",
      description: "",
      url: "",
      budget: 0,
      district: "",
      startDate: new Date().toISOString().split("T")[0],
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
    });
    setIsModalOpen(false);
  };

  // Function to handle project deletion
  const handleDeleteProject = async (projectId: string) => {
    try {
      if (!token) {
        setError("Authentication token not found");
        return;
      }

      await deleteNgoProject(projectId, token);

      // Remove the deleted project from state
      setProjects(projects.filter((project) => project.id !== projectId));
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete project");
      console.error("Error deleting project:", err);
    }
  };

  // Function to refresh project statuses based on current date
  const refreshProjectStatuses = () => {
    const refreshedProjects = projects.map((project) => ({
      ...project,
      status: calculateProjectStatus(project.startDate, project.endDate),
    }));
    setProjects(refreshedProjects);
  };

  // Refresh statuses every minute to keep them current
  useEffect(() => {
    const interval = setInterval(refreshProjectStatuses, 60000); // 1 minute
    return () => clearInterval(interval);
  }, [projects]);

  return (
    <div className="bg-background mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10">
      {error && (
        <div className="mb-4 rounded-md bg-red-100 p-4 text-red-700">
          <p>{error}</p>
          <button
            onClick={() => setError(null)}
            className="ml-2 text-sm font-medium text-red-700 hover:text-red-800"
          >
            Dismiss
          </button>
        </div>
      )}
      <div className="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-title-md2 font-semibold text-black dark:text-white">
            Projects
          </h2>
          <p className="text-bodydark2 text-sm">
            Manage and track all organization projects
          </p>
        </div>
        {/* <button
          onClick={() => setIsModalOpen(true)}
          className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-opacity-90"
        >
          <ProjectIcon className="mr-2 h-4 w-4" />
          Add New Project
        </button> */}
      </div>

      {/* Stats Cards */}
      <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="dark:border-strokedark dark:bg-boxdark rounded-sm border p-4 shadow-default transition-colors">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-black dark:text-white">
                Total Projects
              </p>
              <p className="text-2xl font-bold text-black dark:text-white">
                {projects.length}
              </p>
            </div>
            <div className="flex h-11 w-11 items-center justify-center rounded-full bg-primary bg-opacity-10 dark:bg-opacity-20">
              <ProjectIcon className="h-6 w-6 text-primary" />
            </div>
          </div>
        </div>

        <div className="dark:border-strokedark dark:bg-boxdark rounded-sm border p-4 shadow-default transition-colors">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-black dark:text-white">
                Upcoming Projects
              </p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {upcomingProjects}
              </p>
            </div>
            <div className="flex h-11 w-11 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 dark:bg-opacity-30">
              <div className="h-2 w-2 rounded-full bg-blue-600 dark:bg-blue-400"></div>
            </div>
          </div>
        </div>

        <div className="dark:border-strokedark dark:bg-boxdark rounded-sm border p-4 shadow-default transition-colors">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-black dark:text-white">
                In Progress
              </p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {inProgressProjects}
              </p>
            </div>
            <div className="flex h-11 w-11 items-center justify-center rounded-full bg-green-100 dark:bg-green-900 dark:bg-opacity-30">
              <div className="h-2 w-2 rounded-full bg-green-600 dark:bg-green-400"></div>
            </div>
          </div>
        </div>

        <div className="dark:border-strokedark dark:bg-boxdark rounded-sm border p-4 shadow-default transition-colors">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-black dark:text-white">
                Completed
              </p>
              <p className="text-2xl font-bold text-gray-600 dark:text-gray-400">
                {completedProjects}
              </p>
            </div>
            <div className="flex h-11 w-11 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-900 dark:bg-opacity-30">
              <div className="h-2 w-2 rounded-full bg-gray-600 dark:bg-gray-400"></div>
            </div>
          </div>
        </div>

        <div className="dark:border-strokedark dark:bg-boxdark rounded-sm border p-4 shadow-default transition-colors">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-black dark:text-white">
                Total Budget
              </p>
              <p className="text-2xl font-bold text-black dark:text-white">
                MWK {(totalBudget / 1000000).toFixed(1)}M
              </p>
            </div>
            <div className="flex h-11 w-11 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 dark:bg-opacity-30">
              <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                MWK
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="dark:border-strokedark dark:bg-boxdark w-full rounded-lg border px-4 py-2 pl-10 text-sm transition-colors focus:border-primary focus:outline-none dark:text-white"
            />
            <svg
              className="text-bodydark2 absolute left-3 top-2.5 h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="dark:border-strokedark dark:bg-boxdark rounded-lg border px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="upcoming">Upcoming</option>
            <option value="in progress">In Progress</option>
            <option value="completed">Completed</option>
          </select>

          <select
            value={sectorFilter}
            onChange={(e) => setSectorFilter(e.target.value)}
            className="dark:border-strokedark dark:bg-boxdark rounded-lg border px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:text-white"
          >
            <option value="all">All Sectors</option>
            {sectors.map((sector) => (
              <option key={sector} value={sector}>
                {sector}
              </option>
            ))}
          </select>
        </div>

        <div className="text-bodydark2 text-sm">
          {filteredProjects.length} of {projects.length} projects
        </div>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {isLoading ? (
          // Loading state
          <div className="col-span-3 flex items-center justify-center py-12">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <span className="ml-3 text-sm">Loading projects...</span>
          </div>
        ) : filteredProjects.length === 0 ? (
          // Empty state
          <div className="col-span-3 flex flex-col items-center justify-center py-12">
            <ProjectIcon className="mb-4 h-12 w-12 text-gray-300" />
            <p className="text-lg font-medium text-gray-500">
              No projects found
            </p>
            <p className="text-sm text-gray-400">
              {searchTerm || statusFilter !== "all" || sectorFilter !== "all"
                ? "Try adjusting your filters"
                : "Create your first project to get started"}
            </p>
          </div>
        ) : (
          // Project cards
          filteredProjects.map((project) => (
            <div
              key={project.id}
              className="dark:border-strokedark dark:bg-boxdark rounded-sm border border-stroke bg-white p-6 shadow-default transition-all hover:shadow-lg"
            >
              <div className="mb-4 flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="mb-2 text-lg font-semibold text-black dark:text-white">
                    {project.name}
                  </h3>
                  <p className="text-bodydark2 mb-3 line-clamp-2 text-sm">
                    {project.description}
                  </p>
                </div>
                <span
                  className={`inline-flex rounded-full px-2 py-1 text-xs font-medium transition-colors ${statusColors[project.status]}`}
                >
                  {statusLabels[project.status]}
                </span>
              </div>

              <div className="mb-4">
                <div className="mb-2 flex items-center justify-between text-sm">
                  <span className="text-bodydark2">Progress</span>
                  <span className="font-medium text-black dark:text-white">
                    {project.progress}%
                  </span>
                </div>
                <div className="dark:bg-strokedark h-2 w-full rounded-full bg-stroke">
                  <div
                    className="h-2 rounded-full bg-primary transition-all"
                    style={{ width: `${project.progress}%` }}
                  ></div>
                </div>
              </div>

              <div className="mb-4 space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-bodydark2">Organization:</span>
                  <span className="font-medium text-black dark:text-white">
                    {project.organization}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-bodydark2">Sector:</span>
                  <span className="font-medium text-black dark:text-white">
                    {project.sector}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-bodydark2">District:</span>
                  <span className="font-medium text-black dark:text-white">
                    {project.district}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-bodydark2">Budget:</span>
                  <span className="font-medium text-black dark:text-white">
                    MWK {project.budget.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-bodydark2">Duration:</span>
                  <span className="font-medium text-black dark:text-white">
                    {project.startDate} - {project.endDate}
                  </span>
                </div>
              </div>

              <div className="mb-4">
                <button className="flex items-center gap-1 text-sm font-medium text-primary hover:underline">
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                  View Details
                </button>
              </div>

              {/* Project Actions */}
              <div className="dark:border-strokedark flex justify-between border-t border-stroke pt-4">
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Status automatically calculated from project dates
                  </span>
                  <button
                    onClick={refreshProjectStatuses}
                    className="rounded bg-gray-100 px-2 py-1 text-xs font-medium text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
                    title="Refresh status calculation"
                  >
                    🔄 Refresh
                  </button>
                </div>
                <button
                  onClick={() => {
                    if (
                      window.confirm(
                        "Are you sure you want to delete this project?",
                      )
                    ) {
                      handleDeleteProject(project.id);
                    }
                  }}
                  className="rounded bg-red-100 px-2 py-1 text-xs font-medium text-red-700 hover:bg-red-200 dark:bg-red-900 dark:bg-opacity-30 dark:text-red-400 dark:hover:bg-opacity-50"
                >
                  Delete
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Add Project Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-dark">
            <h3 className="mb-4 text-lg font-semibold text-black dark:text-white">
              Add New Project
            </h3>
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="title"
                  className="mb-2.5 block text-sm font-medium text-black dark:text-white"
                >
                  Project Title
                </label>
                <input
                  id="title"
                  type="text"
                  placeholder="Enter project title"
                  value={newProject.title}
                  onChange={(e) =>
                    setNewProject({ ...newProject, title: e.target.value })
                  }
                  className="dark:border-strokedark dark:bg-boxdark w-full rounded-lg border border-stroke bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label
                  htmlFor="description"
                  className="mb-2.5 block text-sm font-medium text-black dark:text-white"
                >
                  Description
                </label>
                <textarea
                  id="description"
                  rows={3}
                  placeholder="Enter project description"
                  value={newProject.description}
                  onChange={(e) =>
                    setNewProject({
                      ...newProject,
                      description: e.target.value,
                    })
                  }
                  className="dark:border-strokedark dark:bg-boxdark w-full rounded-lg border border-stroke bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:bg-gray-700 dark:text-white"
                ></textarea>
              </div>

              <div>
                <label
                  htmlFor="budget"
                  className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-200"
                >
                  Budget (MWK)
                </label>
                <input
                  id="budget"
                  type="number"
                  placeholder="Enter project budget"
                  value={newProject.budget}
                  onChange={(e) =>
                    setNewProject({
                      ...newProject,
                      budget: parseInt(e.target.value) || 0,
                    })
                  }
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm text-black transition-colors placeholder:text-gray-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400"
                />
              </div>

              <div>
                <label
                  htmlFor="district"
                  className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-200"
                >
                  District
                </label>
                <select
                  id="district"
                  value={newProject.district}
                  onChange={(e) =>
                    setNewProject({ ...newProject, district: e.target.value })
                  }
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm text-black transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select a district</option>
                  <option value="Balaka">Balaka</option>
                  <option value="Blantyre">Blantyre</option>
                  <option value="Chikwawa">Chikwawa</option>
                  <option value="Chiradzulu">Chiradzulu</option>
                  <option value="Chitipa">Chitipa</option>
                  <option value="Dedza">Dedza</option>
                  <option value="Dowa">Dowa</option>
                  <option value="Karonga">Karonga</option>
                  <option value="Kasungu">Kasungu</option>
                  <option value="Likoma">Likoma</option>
                  <option value="Lilongwe">Lilongwe</option>
                  <option value="Machinga">Machinga</option>
                  <option value="Mangochi">Mangochi</option>
                  <option value="Mchinji">Mchinji</option>
                  <option value="Mulanje">Mulanje</option>
                  <option value="Mwanza">Mwanza</option>
                  <option value="Mzimba">Mzimba</option>
                  <option value="Neno">Neno</option>
                  <option value="Nkhata Bay">Nkhata Bay</option>
                  <option value="Nkhotakota">Nkhotakota</option>
                  <option value="Nsanje">Nsanje</option>
                  <option value="Ntcheu">Ntcheu</option>
                  <option value="Ntchisi">Ntchisi</option>
                  <option value="Phalombe">Phalombe</option>
                  <option value="Rumphi">Rumphi</option>
                  <option value="Salima">Salima</option>
                  <option value="Thyolo">Thyolo</option>
                  <option value="Zomba">Zomba</option>
                </select>
              </div>

              {/* <div>
                <label
                  htmlFor="budget"
                  className="mb-2.5 block text-sm font-medium text-black dark:text-white"
                >
                  Budget (MWK)
                </label>
                <input
                  id="budget"
                  type="number"
                  placeholder="Enter project budget"
                  value={newProject.budget}
                  onChange={(e) =>
                    setNewProject({
                      ...newProject,
                      budget: parseInt(e.target.value),
                    })
                  }
                  className="dark:border-strokedark dark:bg-boxdark w-full rounded-lg border border-stroke bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:text-white"
                />
              </div>

              <div>
                <label
                  htmlFor="district"
                  className="mb-2.5 block text-sm font-medium text-black dark:text-white"
                >
                  District
                </label>
                <input
                  id="district"
                  type="text"
                  placeholder="Enter project district"
                  value={newProject.district}
                  onChange={(e) =>
                    setNewProject({ ...newProject, district: e.target.value })
                  }
                  className="dark:border-strokedark dark:bg-boxdark w-full rounded-lg border border-stroke bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:text-white"
                />
              </div> */}

              <div className="flex gap-4">
                <div className="flex-1">
                  <label
                    htmlFor="startDate"
                    className="dark:border-strokedark dark:bg-boxdark mb-2.5 block text-sm font-medium text-black dark:text-white"
                  >
                    Start Date
                  </label>
                  <input
                    id="startDate"
                    type="date"
                    value={newProject.startDate}
                    onChange={(e) =>
                      setNewProject({
                        ...newProject,
                        startDate: e.target.value,
                      })
                    }
                    className="dark:border-strokedark w-full rounded-lg border px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div className="flex-1">
                  <label
                    htmlFor="endDate"
                    className="mb-2.5 block text-sm font-medium text-black dark:text-white"
                  >
                    End Date
                  </label>
                  <input
                    id="endDate"
                    type="date"
                    value={newProject.endDate}
                    onChange={(e) =>
                      setNewProject({ ...newProject, endDate: e.target.value })
                    }
                    className="w-full rounded-lg border border-stroke px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="url"
                  className="mb-2.5 block text-sm font-medium text-black dark:text-white"
                >
                  Project URL (Optional)
                </label>
                <input
                  id="url"
                  type="text"
                  placeholder="Enter project URL"
                  value={newProject.url}
                  onChange={(e) =>
                    setNewProject({ ...newProject, url: e.target.value })
                  }
                  className="dark:border-strokedark w-full rounded-lg border border-stroke px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end gap-4">
              <button
                onClick={handleCancel}
                className="dark:border-strokedark rounded-md border border-stroke px-4 py-2 text-sm font-medium text-black transition-colors hover:bg-gray-100 dark:text-white dark:hover:bg-opacity-10"
              >
                Cancel
              </button>
              <button
                onClick={handleAddProject}
                className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-opacity-90 disabled:cursor-not-allowed disabled:opacity-70"
                disabled={!newProject.title || !newProject.description}
              >
                Add Project
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
