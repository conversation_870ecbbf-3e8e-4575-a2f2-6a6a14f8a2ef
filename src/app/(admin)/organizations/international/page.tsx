"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Modal } from "@/components/ui/modal";
import { OrganizationForm } from "@/components/OrganizationForm";
import {
  SearchIcon,
  PlusIcon,
  EyeIcon,
  MoreVertical,
  CheckCircle,
  Edit2,
  Trash2,
  FilterIcon,
  XIcon,
  ChevronDown,
} from "lucide-react";
import { format } from "date-fns";
import {
  getAllNGOs,
  updateNgo,
  INgo,
  createNgo,
} from "@/services/ngo.services";
import { getAllSectors, Sector } from "@/services/sector.services";
import { getAllDistricts, getAllRegions } from "@/services/location.services";
import { Loader2 } from "lucide-react";
import { ExistingOrganizationForm } from "@/components/ExistingOrganizationForm";

const statusOptions = ["active", "inactive"];

export default function InternationalOrganizationsPage() {
  const router = useRouter();
  const [ngos, setNgos] = useState<INgo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [organizationSubmitted, setOrganizationSubmitted] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: [] as string[],
    areaOfFocus: [] as string[],
    region: [] as string[],
  });
  const [sectors, setSectors] = useState<Sector[]>([]);
  const [regions, setRegions] = useState<string[]>([]);
  const [loadingSectors, setLoadingSectors] = useState(false);
  const [loadingRegions, setLoadingRegions] = useState(false);
  const [showAddDropdown, setShowAddDropdown] = useState(false);
  const [selectedAddOption, setSelectedAddOption] = useState<
    "new" | "existing" | null
  >(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 10;

  // Fetch NGOs from the API
  const fetchNGOs = async () => {
    try {
      setLoading(true);
      setError(null);

      const queryParams = {
        page: currentPage,
        limit: pageSize,
        type: "international" as const,
        ...(search && { search }),
        ...(filters.status.length > 0 && { status: filters.status[0] as any }),
        sortBy: "name",
        sortOrder: "asc" as const,
      };

      // Get token from your auth system
      const token = localStorage.getItem("accessToken")!;
      const response = await getAllNGOs(queryParams, token);

      if (response.status === "success" && response.data) {
        setNgos(response.data);
        if (response.pagination) {
          setTotalCount(response.pagination.total);
          setTotalPages(Math.ceil(response.pagination.total / pageSize));
        }
      } else {
        throw new Error(response.message || "Failed to fetch organizations");
      }
    } catch (err) {
      console.error("Error fetching NGOs:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch organizations",
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch sectors from the API
  const fetchSectors = async () => {
    try {
      setLoadingSectors(true);
      const token = localStorage.getItem("accessToken") || "";
      const response = await getAllSectors({ status: "active" }, token);

      if (response.status === "success" && response.data) {
        setSectors(response.data);
      } else {
        console.error("Failed to fetch sectors:", response.message);
      }
    } catch (err) {
      console.error("Error fetching sectors:", err);
    } finally {
      setLoadingSectors(false);
    }
  };

  // Fetch regions from the API
  const fetchRegions = async () => {
    try {
      setLoadingRegions(true);
      const response = await getAllRegions();

      if (response.success && response.data) {
        setRegions(response.data);
      } else {
        console.error("Failed to fetch regions:", response.message);
        // Fallback to default regions if API fails
        setRegions(["North", "Central", "South"]);
      }
    } catch (err) {
      console.error("Error fetching regions:", err);
      // Fallback to default regions if API fails
      setRegions(["North", "Central", "South"]);
    } finally {
      setLoadingRegions(false);
    }
  };

  // Effect to fetch NGOs when component mounts or dependencies change
  useEffect(() => {
    fetchNGOs();
  }, [currentPage, search, filters.status]);

  // Effect to fetch sectors when component mounts
  useEffect(() => {
    fetchSectors();
    fetchRegions();
  }, []);

  // Debounced search effect
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (currentPage !== 1) {
        setCurrentPage(1);
      } else {
        fetchNGOs();
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [search]);

  const toggleFilter = (filterType: keyof typeof filters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: [],
      areaOfFocus: [],
      region: [],
    });
    setCurrentPage(1);
  };

  // Client-side filtering for non-API supported filters
  const filtered = ngos.filter((org) => {
    const matchesArea =
      filters.areaOfFocus.length === 0 ||
      (org.sectorsOfOperation &&
        Array.isArray(org.sectorsOfOperation) &&
        filters.areaOfFocus.some((areaId) =>
          org.sectorsOfOperation?.some((sector) =>
            typeof sector === "string"
              ? sector === areaId
              : sector._id === areaId,
          ),
        ));
    const matchesRegion =
      filters.region.length === 0 ||
      (org.locationsOfOperation &&
        Array.isArray(org.locationsOfOperation) &&
        filters.region.some((region) =>
          org.locationsOfOperation?.includes(region),
        ));

    return matchesArea && matchesRegion;
  });

  const handleOrganizationSubmit = async (ngoData: FormData) => {
    try {
      setLoading(true);
      console.log("Organization application submitted");

      // Get token from your auth system
      const token = localStorage.getItem("accessToken")!;

      // Use the service to create the NGO with admin
      if (selectedAddOption === "new") {
        // Add type to the form data
        ngoData.append("type", "international");
        await createNgo(ngoData, token);
      } else {
        // For existing organizations
        ngoData.append("type", "international");
        await createNgo(ngoData, token);
      }

      setOrganizationSubmitted(true);
      setShowAddModal(false);
      setSelectedAddOption(null);

      // Refresh the list after adding
      await fetchNGOs();

      setTimeout(() => setOrganizationSubmitted(false), 5000);
    } catch (err) {
      console.error("Error handling organization submission:", err);
      setError(
        err instanceof Error ? err.message : "Failed to submit organization",
      );
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy");
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getApprovalStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleAction = async (action: string, ngoId: string) => {
    setSelectedOrg(null);
    console.log(`${action} organization with ID: ${ngoId}`);

    try {
      // Get token from your auth system
      const token = localStorage.getItem("accessToken")!;

      if (action === "Approve") {
        // Create a FormData object for the update
        const formData = new FormData();
        formData.append("approveStatus", "approved");

        // Use the updateNgo function that accepts FormData
        await updateNgo(ngoId, formData, token);
        await fetchNGOs(); // Refresh data
      } else if (action === "Dissolve") {
        // Handle dissolve action
        const formData = new FormData();
        formData.append("status", "inactive");
        await updateNgo(ngoId, formData, token);
        await fetchNGOs(); // Refresh data
      } else if (action === "View") {
        // Navigate to organization details page
        router.push(`/organizations/${ngoId}`);
      } else if (action === "Edit") {
        // Handle edit action - could open a modal or navigate to edit page
        console.log(`Edit organization: ${ngoId}`);
        // TODO: Implement edit functionality
      }
    } catch (err) {
      console.error(`Error performing ${action} action:`, err);
      setError(
        err instanceof Error
          ? err.message
          : `Failed to ${action.toLowerCase()} organization`,
      );
    }
  };

  const handleAddOptionSelect = (option: "new" | "existing") => {
    setSelectedAddOption(option);
    setShowAddDropdown(false);
    setShowAddModal(true);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    const startPage = Math.max(
      1,
      currentPage - Math.floor(maxVisiblePages / 2),
    );
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="mt-6 flex items-center justify-between">
        <div className="text-sm text-gray-700 dark:text-gray-300">
          Showing {(currentPage - 1) * pageSize + 1} to{" "}
          {Math.min(currentPage * pageSize, totalCount)} of {totalCount} results
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            Previous
          </button>
          {pages.map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`rounded-md px-3 py-2 text-sm font-medium ${
                page === currentPage
                  ? "bg-blue-600 text-white"
                  : "border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
              }`}
            >
              {page}
            </button>
          ))}
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            Next
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      {organizationSubmitted && (
        <div className="mb-6 rounded-lg bg-green-100 p-4 text-green-800">
          Organization application submitted successfully!
        </div>
      )}

      <div className="mb-8 flex flex-col justify-between gap-6 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            International Organizations
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage and view all registered international NGOs ({totalCount}{" "}
            total)
          </p>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="Search organizations..."
              className="w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <FilterIcon className="h-4 w-4" />
              Filters
              {Object.values(filters).some((arr) => arr.length > 0) && (
                <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {Object.values(filters).flat().length}
                </span>
              )}
            </button>

            <div className="relative">
              <button
                onClick={() => setShowAddDropdown(!showAddDropdown)}
                className="flex items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <PlusIcon className="h-4 w-4" />
                Add Organization
                <ChevronDown
                  className={`h-4 w-4 transition-transform ${showAddDropdown ? "rotate-180" : ""}`}
                />
              </button>

              {showAddDropdown && (
                <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800">
                  <div className="py-1">
                    <button
                      onClick={() => handleAddOptionSelect("new")}
                      className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                    >
                      New Int. NGO
                    </button>
                    <button
                      onClick={() => handleAddOptionSelect("existing")}
                      className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                    >
                      Existing Int. NGO
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {showFilters && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <div className="flex gap-2">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <XIcon className="h-4 w-4" />
                Clear all
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              >
                <XIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-3">
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </h4>
              <div className="space-y-2">
                {statusOptions.map((status, index) => (
                  <div
                    key={`status-${status}-${index}`}
                    className="flex items-center"
                  >
                    <input
                      id={`status-${status}`}
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={() => toggleFilter("status", status)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`status-${status}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Area of Focus
              </h4>
              <div className="space-y-2">
                {loadingSectors ? (
                  <div className="flex items-center justify-center py-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                    <span className="ml-2 text-sm text-gray-500">
                      Loading...
                    </span>
                  </div>
                ) : sectors.length > 0 ? (
                  sectors.map((sector) => (
                    <div key={sector._id} className="flex items-center">
                      <input
                        id={`area-${sector._id}`}
                        type="checkbox"
                        checked={filters.areaOfFocus.includes(sector._id || "")}
                        onChange={() =>
                          toggleFilter("areaOfFocus", sector._id || "")
                        }
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                      />
                      <label
                        htmlFor={`area-${sector._id}`}
                        className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >
                        {sector.name}
                      </label>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500">
                    No sectors available
                  </div>
                )}
              </div>
            </div>

            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Region
              </h4>
              <div className="space-y-2">
                {loadingRegions ? (
                  <div className="flex items-center justify-center py-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                    <span className="ml-2 text-sm text-gray-500">
                      Loading...
                    </span>
                  </div>
                ) : regions.length > 0 ? (
                  regions.map((region) => (
                    <div key={region} className="flex items-center">
                      <input
                        id={`region-${region}`}
                        type="checkbox"
                        checked={filters.region.includes(region)}
                        onChange={() => toggleFilter("region", region)}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                      />
                      <label
                        htmlFor={`region-${region}`}
                        className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >
                        {region}
                      </label>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500">
                    No regions available
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {Object.values(filters).some((arr) => arr.length > 0) && (
        <div className="mb-4 flex flex-wrap gap-2">
          {filters.status.map((status) => (
            <span
              key={`status-${status}`}
              className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              Status: {status.charAt(0).toUpperCase() + status.slice(1)}
              <button
                onClick={() => toggleFilter("status", status)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-800 hover:bg-blue-200 dark:text-blue-200 dark:hover:bg-blue-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
          {filters.areaOfFocus.map((areaId) => {
            const sectorName =
              sectors.find((s) => s._id === areaId)?.name || areaId;
            return (
              <span
                key={`area-${areaId}`}
                className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200"
              >
                Focus: {sectorName}
                <button
                  onClick={() => toggleFilter("areaOfFocus", areaId)}
                  className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-800 hover:bg-green-200 dark:text-green-200 dark:hover:bg-green-800"
                >
                  <XIcon className="h-3 w-3" />
                </button>
              </span>
            );
          })}
          {filters.region.map((region) => (
            <span
              key={`region-${region}`}
              className="inline-flex items-center rounded-full bg-purple-100 px-3 py-1 text-xs font-medium text-purple-800 dark:bg-purple-900 dark:text-purple-200"
            >
              Region: {region}
              <button
                onClick={() => toggleFilter("region", region)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-purple-800 hover:bg-purple-200 dark:text-purple-200 dark:hover:bg-purple-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm dark:border-gray-700">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                Loading organizations...
              </span>
            </div>
          ) : (
            <Table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <TableHeader>
                <TableRow className="bg-gray-50 dark:bg-gray-800">
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    NGO Name
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Initials
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Status
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Approval Status
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Areas of Focus
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Date Formed
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    CEO
                  </TableHead>
                  <TableHead className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                {filtered.map((org) => (
                  <TableRow
                    key={org._id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                  >
                    <TableCell className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 dark:text-white">
                      {org.name}
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4">
                      <span className="inline-flex items-center rounded-full border border-gray-300 bg-gray-50 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300">
                        {org.initials}
                      </span>
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4">
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(
                          org.status || "inactive",
                        )}`}
                      >
                        {org.status
                          ? org.status.charAt(0).toUpperCase() +
                            org.status.slice(1)
                          : "Inactive"}
                      </span>
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4">
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(
                          org.approvedStatus || "pending",
                        )}`}
                      >
                        {org.approvedStatus
                          ? org.approvedStatus.charAt(0).toUpperCase() +
                            org.approvedStatus.slice(1)
                          : "Pending"}
                      </span>
                    </TableCell>
                    <TableCell className="px-6 py-4 text-gray-600 dark:text-gray-400">
                      <div className="max-w-32 truncate">
                        {org.sectorsOfOperation &&
                        Array.isArray(org.sectorsOfOperation) &&
                        org.sectorsOfOperation.length > 0
                          ? org.sectorsOfOperation
                              .map((sector) => {
                                // Handle both string IDs and objects with name property
                                if (typeof sector === "string") {
                                  // Find the sector name in our sectors array
                                  const sectorObj = sectors.find(
                                    (s) => s._id === sector,
                                  );
                                  return sectorObj ? sectorObj.name : sector;
                                } else {
                                  return sector.name || "Unknown";
                                }
                              })
                              .join(", ")
                          : "Not specified"}
                      </div>
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4 text-gray-600 dark:text-gray-400">
                      {formatDate(org.dateFounded)}
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4 text-gray-600 dark:text-gray-400">
                      {org.chiefExecutiveName}
                    </TableCell>
                    <TableCell className="whitespace-nowrap px-6 py-4 text-right">
                      <div className="relative inline-block text-left">
                        <div>
                          <button
                            type="button"
                            onClick={() =>
                              setSelectedOrg(
                                selectedOrg === org._id ? null : org._id,
                              )
                            }
                            className="inline-flex items-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600 focus:outline-none dark:hover:bg-gray-700 dark:hover:text-gray-300"
                            id={`menu-button-${org._id}`}
                            aria-expanded={selectedOrg === org._id}
                            aria-haspopup="true"
                          >
                            <MoreVertical className="h-5 w-5" />
                          </button>
                        </div>

                        {selectedOrg === org._id && (
                          <div
                            className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800"
                            role="menu"
                            aria-orientation="vertical"
                            aria-labelledby={`menu-button-${org._id}`}
                          >
                            <div className="py-1" role="none">
                              <button
                                onClick={() => handleAction("View", org._id)}
                                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                role="menuitem"
                              >
                                <EyeIcon className="mr-3 h-4 w-4" />
                                View Details
                              </button>
                              <button
                                onClick={() => handleAction("Edit", org._id)}
                                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                role="menuitem"
                              >
                                <Edit2 className="mr-3 h-4 w-4" />
                                Edit Organization
                              </button>
                              <button
                                onClick={() => handleAction("Approve", org._id)}
                                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                role="menuitem"
                              >
                                <CheckCircle className="mr-3 h-4 w-4" />
                                Approve
                              </button>
                              <button
                                onClick={() =>
                                  handleAction("Dissolve", org._id)
                                }
                                className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:text-red-500 dark:hover:bg-gray-700"
                                role="menuitem"
                              >
                                <Trash2 className="mr-3 h-4 w-4" />
                                Dissolve Organization
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {filtered.length === 0 && !loading && (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      className="py-12 text-center text-gray-500"
                    >
                      <div className="flex flex-col items-center justify-center gap-2">
                        <SearchIcon className="h-8 w-8 text-gray-400" />
                        <p className="text-lg font-medium">
                          No organizations found
                        </p>
                        <p className="text-sm text-gray-500">
                          Try adjusting your search or filters
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </div>

      {renderPagination()}

      <Modal
        isOpen={showAddModal}
        onClose={() => {
          setShowAddModal(false);
          setSelectedAddOption(null);
        }}
        title={
          selectedAddOption === "existing"
            ? "Register Existing International Organization"
            : "Register New International Organization"
        }
      >
        <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
          {selectedAddOption === "existing"
            ? "Fill out the form to register an existing international NGO"
            : "Fill out the form to register a new international NGO"}
        </div>
        {selectedAddOption === "existing" ? (
          <ExistingOrganizationForm
            onSubmit={handleOrganizationSubmit}
            onCancel={() => {
              setShowAddModal(false);
              setSelectedAddOption(null);
            }}
            type="international"
            isExisting={true}
          />
        ) : (
          <OrganizationForm
            onSubmit={handleOrganizationSubmit}
            onCancel={() => {
              setShowAddModal(false);
              setSelectedAddOption(null);
            }}
            type="international"
            isExisting={false}
          />
        )}
      </Modal>
    </div>
  );
}
