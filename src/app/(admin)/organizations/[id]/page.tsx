// // "use client";

// // import React, { useState, useEffect } from "react";
// // import { useParams, useRouter } from "next/navigation";
// // import {
// //   HiOutlineBuildingOffice2,
// //   HiOutlineUser,
// //   HiOutlineEnvelope,
// //   HiOutlinePhone,
// //   HiOutlineMapPin,
// //   HiOutlineCalendar,
// //   HiOutlineDocumentText,
// //   HiOutlineEye,
// //   HiOutlineArrowLeft,
// //   HiOutlineCheckCircle,
// //   HiOutlineXCircle,
// //   HiOutlineClock,
// // } from "react-icons/hi2";
// // import { getNGOById, INgo } from "@/services/ngo.services";
// // import {
// //   getOrganizationDocuments,
// //   OrganizationDocument,
// // } from "@/services/organization-documents.services";
// // import { format } from "date-fns";

// // export default function OrganizationDetailsPage() {
// //   const params = useParams();
// //   const router = useRouter();
// //   const organizationId = params.id as string;

// //   const [organization, setOrganization] = useState<INgo | null>(null);
// //   const [documents, setDocuments] = useState<OrganizationDocument[]>([]);
// //   const [loading, setLoading] = useState(true);
// //   const [documentsLoading, setDocumentsLoading] = useState(true);
// //   const [error, setError] = useState<string | null>(null);

// //   useEffect(() => {
// //     if (organizationId) {
// //       fetchOrganizationDetails();
// //       fetchOrganizationDocuments();
// //     }
// //   }, [organizationId]);

// //   const fetchOrganizationDetails = async () => {
// //     try {
// //       setLoading(true);
// //       const token = localStorage.getItem("accessToken");
// //       if (!token) {
// //         setError("Authentication required");
// //         return;
// //       }

// //       const response = await getNGOById(organizationId, token);
// //       if (response.status === "success" && response.data) {
// //         setOrganization(response.data);
// //         console.log("organization: ", response.data);
// //       } else {
// //         setError(response.message || "Failed to fetch organization details");
// //       }
// //     } catch (err) {
// //       setError("An error occurred while fetching organization details");
// //       console.error(err);
// //     } finally {
// //       setLoading(false);
// //     }
// //   };

// //   const fetchOrganizationDocuments = async () => {
// //     try {
// //       setDocumentsLoading(true);
// //       const token = localStorage.getItem("accessToken");
// //       if (!token) {
// //         return;
// //       }

// //       const response = await getOrganizationDocuments(organizationId, token);
// //       if (response.status === "success" && response.data) {
// //         setDocuments(response.data.documents);
// //       }
// //     } catch (err) {
// //       console.error("Failed to fetch documents:", err);
// //     } finally {
// //       setDocumentsLoading(false);
// //     }
// //   };

// //   const getStatusColor = (status: string) => {
// //     switch (status.toLowerCase()) {
// //       case "approved":
// //         return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
// //       case "pending":
// //         return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
// //       case "rejected":
// //         return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
// //       default:
// //         return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
// //     }
// //   };

// //   const getStatusIcon = (status: string) => {
// //     switch (status.toLowerCase()) {
// //       case "approved":
// //       case "uploaded":
// //         return <HiOutlineCheckCircle className="h-5 w-5 text-green-600" />;
// //       case "pending":
// //         return <HiOutlineClock className="h-5 w-5 text-yellow-600" />;
// //       case "rejected":
// //       case "missing":
// //         return <HiOutlineXCircle className="h-5 w-5 text-red-600" />;
// //       default:
// //         return <HiOutlineClock className="h-5 w-5 text-gray-600" />;
// //     }
// //   };

// //   const formatDate = (dateString: string | undefined) => {
// //     if (!dateString) return "N/A";
// //     try {
// //       return format(new Date(dateString), "MMM dd, yyyy");
// //     } catch {
// //       return "N/A";
// //     }
// //   };

// //   const handleViewDocument = (document: OrganizationDocument) => {
// //     if (document.url) {
// //       window.open(document.url, "_blank");
// //     } else {
// //       alert(`Document "${document.name}" is not available for viewing.`);
// //     }
// //   };

// //   if (loading) {
// //     return (
// //       <div className="flex items-center justify-center py-12">
// //         <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
// //         <span className="ml-2 text-gray-600 dark:text-gray-400">
// //           Loading organization details...
// //         </span>
// //       </div>
// //     );
// //   }

// //   if (error) {
// //     return (
// //       <div className="rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
// //         <p>{error}</p>
// //         <button
// //           onClick={() => router.back()}
// //           className="mt-2 text-sm underline hover:no-underline"
// //         >
// //           Go back
// //         </button>
// //       </div>
// //     );
// //   }

// //   if (!organization) {
// //     return (
// //       <div className="py-12 text-center">
// //         <p className="text-gray-600 dark:text-gray-400">
// //           Organization not found
// //         </p>
// //         <button
// //           onClick={() => router.back()}
// //           className="mt-2 text-sm text-blue-600 underline hover:no-underline"
// //         >
// //           Go back
// //         </button>
// //       </div>
// //     );
// //   }

// //   return (
// //     <div className="space-y-6">
// //       {/* Header */}
// //       <div className="flex items-center justify-between">
// //         <div className="flex items-center gap-4">
// //           <button
// //             onClick={() => router.back()}
// //             className="flex items-center gap-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
// //           >
// //             <HiOutlineArrowLeft className="h-5 w-5" />
// //             Back
// //           </button>
// //           <div>
// //             <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
// //               {organization.name}
// //             </h1>
// //             <p className="text-gray-600 dark:text-gray-400">
// //               Organization Details
// //             </p>
// //           </div>
// //         </div>
// //         <div className="flex items-center gap-2">
// //           <span
// //             className={`inline-flex items-center gap-1 rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(
// //               organization.approvedStatus || "pending",
// //             )}`}
// //           >
// //             {getStatusIcon(organization.approvedStatus || "pending")}
// //             {organization.approvedStatus
// //               ? organization.approvedStatus.charAt(0).toUpperCase() +
// //                 organization.approvedStatus.slice(1)
// //               : "Pending"}
// //           </span>
// //         </div>
// //       </div>

// //       {/* Organization Details */}
// //       <div className="grid gap-6 lg:grid-cols-2">
// //         {/* Basic Information */}
// //         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
// //           <div className="mb-4 flex items-center gap-2">
// //             <HiOutlineBuildingOffice2 className="h-5 w-5 text-blue-600" />
// //             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
// //               Basic Information
// //             </h2>
// //           </div>

// //           <div className="space-y-4">
// //             <div>
// //               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
// //                 Organization Name
// //               </label>
// //               <p className="mt-1 text-gray-900 dark:text-white">
// //                 {organization.name}
// //               </p>
// //             </div>

// //             <div>
// //               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
// //                 Organization Initials
// //               </label>
// //               <p className="mt-1 text-gray-900 dark:text-white">
// //                 {organization.initials}
// //               </p>
// //             </div>

// //             <div>
// //               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
// //                 Type
// //               </label>
// //               <p className="mt-1 text-gray-900 dark:text-white">
// //                 {organization.type}
// //               </p>
// //             </div>

// //             <div>
// //               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
// //                 Headquarters Address
// //               </label>
// //               <p className="mt-1 text-gray-900 dark:text-white">
// //                 {organization.headquartersAddress}
// //               </p>
// //             </div>

// //             <div>
// //               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
// //                 Date Founded
// //               </label>
// //               <p className="mt-1 text-gray-900 dark:text-white">
// //                 {formatDate(organization.dateFounded)}
// //               </p>
// //             </div>

// //             {organization.dateApprovedByGOM && (
// //               <div>
// //                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
// //                   Date Approved by Government
// //                 </label>
// //                 <p className="mt-1 text-gray-900 dark:text-white">
// //                   {formatDate(organization.dateApprovedByGOM)}
// //                 </p>
// //               </div>
// //             )}
// //           </div>
// //         </div>

// //         {/* Contact Information */}
// //         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
// //           <div className="mb-4 flex items-center gap-2">
// //             <HiOutlineUser className="h-5 w-5 text-blue-600" />
// //             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
// //               Leadership
// //             </h2>
// //           </div>

// //           <div className="space-y-6">
// //             {/* Chairperson */}
// //             <div>
// //               <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                 Chairperson
// //               </h3>
// //               <div className="space-y-2">
// //                 <div className="flex items-center gap-2">
// //                   <HiOutlineUser className="h-4 w-4 text-gray-500" />
// //                   <span className="text-gray-900 dark:text-white">
// //                     {organization.chairpersonName}
// //                   </span>
// //                 </div>
// //                 <div className="flex items-center gap-2">
// //                   <HiOutlineEnvelope className="h-4 w-4 text-gray-500" />
// //                   <span className="text-gray-900 dark:text-white">
// //                     {organization.chairpersonEmail}
// //                   </span>
// //                 </div>
// //                 <div className="flex items-center gap-2">
// //                   <HiOutlinePhone className="h-4 w-4 text-gray-500" />
// //                   <span className="text-gray-900 dark:text-white">
// //                     {organization.chairpersonPhone}
// //                   </span>
// //                 </div>
// //               </div>
// //             </div>

// //             {/* Chief Executive */}
// //             <div>
// //               <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                 Chief Executive
// //               </h3>
// //               <div className="space-y-2">
// //                 <div className="flex items-center gap-2">
// //                   <HiOutlineUser className="h-4 w-4 text-gray-500" />
// //                   <span className="text-gray-900 dark:text-white">
// //                     {organization.chiefExecutiveName}
// //                   </span>
// //                 </div>
// //                 <div className="flex items-center gap-2">
// //                   <HiOutlineEnvelope className="h-4 w-4 text-gray-500" />
// //                   <span className="text-gray-900 dark:text-white">
// //                     {organization.chiefExecutiveEmail}
// //                   </span>
// //                 </div>
// //                 <div className="flex items-center gap-2">
// //                   <HiOutlinePhone className="h-4 w-4 text-gray-500" />
// //                   <span className="text-gray-900 dark:text-white">
// //                     {organization.chiefExecutivePhone}
// //                   </span>
// //                 </div>
// //               </div>
// //             </div>
// //           </div>
// //         </div>
// //       </div>

// //       {/* Mission, Vision, Values */}
// //       {(organization.missionStatement ||
// //         organization.visionStatement ||
// //         organization.valuesStatement) && (
// //         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
// //           <div className="mb-4 flex items-center gap-2">
// //             <HiOutlineDocumentText className="h-5 w-5 text-blue-600" />
// //             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
// //               Mission, Vision & Values
// //             </h2>
// //           </div>

// //           <div className="grid gap-6 md:grid-cols-3">
// //             {organization.missionStatement && (
// //               <div>
// //                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                   Mission
// //                 </h3>
// //                 <p className="text-gray-600 dark:text-gray-400">
// //                   {organization.missionStatement}
// //                 </p>
// //               </div>
// //             )}

// //             {organization.visionStatement && (
// //               <div>
// //                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                   Vision
// //                 </h3>
// //                 <p className="text-gray-600 dark:text-gray-400">
// //                   {organization.visionStatement}
// //                 </p>
// //               </div>
// //             )}

// //             {organization.valuesStatement && (
// //               <div>
// //                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                   Values
// //                 </h3>
// //                 <p className="text-gray-600 dark:text-gray-400">
// //                   {organization.valuesStatement}
// //                 </p>
// //               </div>
// //             )}
// //           </div>
// //         </div>
// //       )}

// //       {/* Background and Goals */}
// //       {(organization.ngoBackground || organization.goals) && (
// //         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
// //           <div className="mb-4 flex items-center gap-2">
// //             <HiOutlineDocumentText className="h-5 w-5 text-blue-600" />
// //             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
// //               Background & Goals
// //             </h2>
// //           </div>

// //           <div className="grid gap-6 md:grid-cols-2">
// //             {organization.ngoBackground && (
// //               <div>
// //                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                   Background
// //                 </h3>
// //                 <p className="text-gray-600 dark:text-gray-400">
// //                   {organization.ngoBackground}
// //                 </p>
// //               </div>
// //             )}

// //             {organization.goals && organization.goals.length > 0 && (
// //               <div>
// //                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                   Goals
// //                 </h3>
// //                 <div className="list-inside list-disc space-y-1 text-gray-600 dark:text-gray-400">
// //                   {organization?.goals}
// //                 </div>
// //               </div>
// //             )}
// //           </div>
// //         </div>
// //       )}

// //       {/* Operations */}
// //       {(organization.locationsOfOperation ||
// //         organization.sectorsOfOperation ||
// //         organization.numberOfEmployees) && (
// //         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
// //           <div className="mb-4 flex items-center gap-2">
// //             <HiOutlineMapPin className="h-5 w-5 text-blue-600" />
// //             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
// //               Operations
// //             </h2>
// //           </div>

// //           <div className="grid gap-6 md:grid-cols-3">
// //             {organization.locationsOfOperation &&
// //               organization.locationsOfOperation.length > 0 && (
// //                 <div>
// //                   <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                     Locations of Operation
// //                   </h3>
// //                   <ul className="list-inside list-disc space-y-1 text-gray-600 dark:text-gray-400">
// //                     {organization.locationsOfOperation.map(
// //                       (location, index) => (
// //                         <li key={index}>{location}</li>
// //                       ),
// //                     )}
// //                   </ul>
// //                 </div>
// //               )}

// //             {organization.sectorsOfOperation &&
// //               organization.sectorsOfOperation.length > 0 && (
// //                 <div>
// //                   <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                     Sectors of Operation
// //                   </h3>
// //                   <div className="flex flex-wrap gap-2">
// //                     {organization.sectorsOfOperation.map((sector, index) => (
// //                       <span
// //                         key={index}
// //                         className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
// //                       >
// //                         {sector.name}
// //                       </span>
// //                     ))}
// //                   </div>
// //                 </div>
// //               )}

// //             {organization.numberOfEmployees && (
// //               <div>
// //                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
// //                   Number of Employees
// //                 </h3>
// //                 <p className="text-gray-600 dark:text-gray-400">
// //                   {organization.numberOfEmployees}
// //                 </p>
// //               </div>
// //             )}
// //           </div>
// //         </div>
// //       )}

// //       {/* Documents Section */}
// //       <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
// //         <div className="mb-4 flex items-center gap-2">
// //           <HiOutlineDocumentText className="h-5 w-5 text-blue-600" />
// //           <h2 className="text-xl font-bold text-gray-900 dark:text-white">
// //             Documents
// //           </h2>
// //         </div>

// //         {documentsLoading ? (
// //           <div className="flex items-center justify-center py-8">
// //             <div className="h-6 w-6 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
// //             <span className="ml-2 text-gray-600 dark:text-gray-400">
// //               Loading documents...
// //             </span>
// //           </div>
// //         ) : documents.length > 0 ? (
// //           <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
// //             {documents.map((document) => (
// //               <div
// //                 key={document.id}
// //                 className="rounded-lg border border-gray-200 p-4 dark:border-gray-700"
// //               >
// //                 <div className="mb-2 flex items-center justify-between">
// //                   <h3 className="text-sm font-medium text-gray-900 dark:text-white">
// //                     {document.name}
// //                   </h3>
// //                   {getStatusIcon(document.status)}
// //                 </div>
// //                 <p className="mb-2 text-xs text-gray-500 dark:text-gray-400">
// //                   Status:{" "}
// //                   {document.status.charAt(0).toUpperCase() +
// //                     document.status.slice(1)}
// //                 </p>
// //                 <p className="mb-3 text-xs text-gray-500 dark:text-gray-400">
// //                   Uploaded: {formatDate(document.uploadDate)}
// //                 </p>
// //                 {document.url && (
// //                   <button
// //                     onClick={() => handleViewDocument(document)}
// //                     className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
// //                   >
// //                     <HiOutlineEye className="h-3 w-3" />
// //                     View Document
// //                   </button>
// //                 )}
// //               </div>
// //             ))}
// //           </div>
// //         ) : (
// //           <p className="text-gray-600 dark:text-gray-400">
// //             No documents available for this organization.
// //           </p>
// //         )}
// //       </div>
// //     </div>
// //   );
// // }

// "use client";

// import React, { useState, useEffect } from "react";
// import { useParams, useRouter } from "next/navigation";
// import {
//   HiOutlineBuildingOffice2,
//   HiOutlineUser,
//   HiOutlineEnvelope,
//   HiOutlinePhone,
//   HiOutlineMapPin,
//   HiOutlineCalendar,
//   HiOutlineDocumentText,
//   HiOutlineEye,
//   HiOutlineArrowLeft,
//   HiOutlineCheckCircle,
//   HiOutlineXCircle,
//   HiOutlineClock,
//   HiOutlineExclamationCircle,
// } from "react-icons/hi2";
// import { getNGOById, INgo } from "@/services/ngo.services";
// import {
//   getOrganizationDocuments,
//   OrganizationDocument,
// } from "@/services/organization-documents.services";

// export default function OrganizationDetailsPage() {
//   const params = useParams();
//   const router = useRouter();
//   const organizationId = params.id as string;

//   const [organization, setOrganization] = useState<INgo | null>(null);
//   const [documents, setDocuments] = useState<OrganizationDocument[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [documentsLoading, setDocumentsLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);

//   useEffect(() => {
//     if (organizationId) {
//       fetchOrganizationDetails();
//       fetchOrganizationDocuments();
//     }
//   }, [organizationId]);

//   const fetchOrganizationDetails = async () => {
//     try {
//       setLoading(true);
//       const token = localStorage.getItem("accessToken");
//       if (!token) {
//         setError("Authentication required");
//         return;
//       }

//       const response = await getNGOById(organizationId, token);
//       if (response.status === "success" && response.data) {
//         setOrganization(response.data);
//         console.log("organization: ", response.data);
//       } else {
//         setError(response.message || "Failed to fetch organization details");
//       }
//     } catch (err) {
//       setError("An error occurred while fetching organization details");
//       console.error(err);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const fetchOrganizationDocuments = async () => {
//     try {
//       setDocumentsLoading(true);
//       const token = localStorage.getItem("accessToken");
//       if (!token) {
//         return;
//       }

//       const response = await getOrganizationDocuments(organizationId, token);
//       if (response.status === "success" && response.data) {
//         setDocuments(response.data.documents);
//       }
//     } catch (err) {
//       console.error("Failed to fetch documents:", err);
//     } finally {
//       setDocumentsLoading(false);
//     }
//   };

//   const getStatusColor = (status: string) => {
//     switch (status.toLowerCase()) {
//       case "approved":
//         return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
//       case "pending":
//         return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
//       case "rejected":
//         return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
//       default:
//         return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
//     }
//   };

//   const getStatusIcon = (status: string) => {
//     switch (status.toLowerCase()) {
//       case "approved":
//       case "uploaded":
//         return <HiOutlineCheckCircle className="h-5 w-5 text-green-600" />;
//       case "pending":
//         return <HiOutlineClock className="h-5 w-5 text-yellow-600" />;
//       case "rejected":
//       case "missing":
//         return <HiOutlineXCircle className="h-5 w-5 text-red-600" />;
//       default:
//         return <HiOutlineClock className="h-5 w-5 text-gray-600" />;
//     }
//   };

//   const formatDate = (dateString: string | undefined) => {
//     if (!dateString) return "N/A";
//     try {
//       const date = new Date(dateString);
//       const months = [
//         "Jan",
//         "Feb",
//         "Mar",
//         "Apr",
//         "May",
//         "Jun",
//         "Jul",
//         "Aug",
//         "Sep",
//         "Oct",
//         "Nov",
//         "Dec",
//       ];
//       const month = months[date.getMonth()];
//       const day = date.getDate().toString().padStart(2, "0");
//       const year = date.getFullYear();
//       return `${month} ${day}, ${year}`;
//     } catch {
//       return "N/A";
//     }
//   };

//   const handleViewDocument = (document: OrganizationDocument) => {
//     if (document.url) {
//       window.open(document.url, "_blank");
//     } else {
//       alert(`Document "${document.name}" is not available for viewing.`);
//     }
//   };

//   if (loading) {
//     return (
//       <div className="flex items-center justify-center py-12">
//         <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
//         <span className="ml-2 text-gray-600 dark:text-gray-400">
//           Loading organization details...
//         </span>
//       </div>
//     );
//   }

//   if (error) {
//     return (
//       <div className="rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
//         <p>{error}</p>
//         <button
//           onClick={() => router.back()}
//           className="mt-2 text-sm underline hover:no-underline"
//         >
//           Go back
//         </button>
//       </div>
//     );
//   }

//   if (!organization) {
//     return (
//       <div className="py-12 text-center">
//         <p className="text-gray-600 dark:text-gray-400">
//           Organization not found
//         </p>
//         <button
//           onClick={() => router.back()}
//           className="mt-2 text-sm text-blue-600 underline hover:no-underline"
//         >
//           Go back
//         </button>
//       </div>
//     );
//   }

//   return (
//     <div className="space-y-6">
//       {/* Header */}
//       <div className="flex items-center justify-between">
//         <div className="flex items-center gap-4">
//           <button
//             onClick={() => router.back()}
//             className="flex items-center gap-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
//           >
//             <HiOutlineArrowLeft className="h-5 w-5" />
//             Back
//           </button>
//           <div>
//             <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
//               {organization.name}
//             </h1>
//             <p className="text-gray-600 dark:text-gray-400">
//               Organization Details
//             </p>
//           </div>
//         </div>
//         <div className="flex items-center gap-2">
//           <span
//             className={`inline-flex items-center gap-1 rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(
//               organization.approvedStatus || "pending",
//             )}`}
//           >
//             {getStatusIcon(organization.approvedStatus || "pending")}
//             {organization.approvedStatus
//               ? organization.approvedStatus.charAt(0).toUpperCase() +
//                 organization.approvedStatus.slice(1)
//               : "Pending"}
//           </span>
//         </div>
//       </div>

//       {/* Rejection Reason Alert */}
//       {organization.approvedStatus === "rejected" &&
//         organization.rejectionReason && (
//           <div className="rounded-lg bg-red-50 p-4 dark:bg-red-900/20">
//             <div className="flex items-start gap-3">
//               <HiOutlineExclamationCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-red-600 dark:text-red-400" />
//               <div>
//                 <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
//                   Rejection Reason
//                 </h3>
//                 <p className="mt-1 text-sm text-red-700 dark:text-red-400">
//                   {organization.rejectionReason}
//                 </p>
//               </div>
//             </div>
//           </div>
//         )}

//       {/* Organization Details */}
//       <div className="grid gap-6 lg:grid-cols-2">
//         {/* Basic Information */}
//         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
//           <div className="mb-4 flex items-center gap-2">
//             <HiOutlineBuildingOffice2 className="h-5 w-5 text-blue-600" />
//             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
//               Basic Information
//             </h2>
//           </div>

//           <div className="space-y-4">
//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
//                 Organization Name
//               </label>
//               <p className="mt-1 text-gray-900 dark:text-white">
//                 {organization.name}
//               </p>
//             </div>

//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
//                 Organization Initials
//               </label>
//               <p className="mt-1 text-gray-900 dark:text-white">
//                 {organization.initials}
//               </p>
//             </div>

//             {organization.registrationNumber && (
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
//                   Registration Number
//                 </label>
//                 <p className="mt-1 text-gray-900 dark:text-white">
//                   {organization.registrationNumber}
//                 </p>
//               </div>
//             )}

//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
//                 Type
//               </label>
//               <p className="mt-1 text-gray-900 dark:text-white">
//                 {organization.type}
//               </p>
//             </div>

//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
//                 Headquarters Address
//               </label>
//               <p className="mt-1 text-gray-900 dark:text-white">
//                 {organization.headquartersAddress}
//               </p>
//             </div>

//             {organization.physicalOfficesAddress && (
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
//                   Physical Office Address
//                 </label>
//                 <p className="mt-1 text-gray-900 dark:text-white">
//                   {organization.physicalOfficesAddress}
//                 </p>
//               </div>
//             )}

//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
//                 Date Founded
//               </label>
//               <p className="mt-1 text-gray-900 dark:text-white">
//                 {formatDate(organization.dateFounded)}
//               </p>
//             </div>

//             {organization.dateApprovedByGOM && (
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
//                   Date Approved by Government
//                 </label>
//                 <p className="mt-1 text-gray-900 dark:text-white">
//                   {formatDate(organization.dateApprovedByGOM)}
//                 </p>
//               </div>
//             )}
//           </div>
//         </div>

//         {/* Contact Information */}
//         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
//           <div className="mb-4 flex items-center gap-2">
//             <HiOutlineUser className="h-5 w-5 text-blue-600" />
//             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
//               Leadership
//             </h2>
//           </div>

//           <div className="space-y-6">
//             {/* Chairperson */}
//             <div>
//               <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                 Chairperson
//               </h3>
//               <div className="space-y-2">
//                 <div className="flex items-center gap-2">
//                   <HiOutlineUser className="h-4 w-4 text-gray-500" />
//                   <span className="text-gray-900 dark:text-white">
//                     {organization.chairpersonName}
//                   </span>
//                 </div>
//                 <div className="flex items-center gap-2">
//                   <HiOutlineEnvelope className="h-4 w-4 text-gray-500" />
//                   <span className="text-gray-900 dark:text-white">
//                     {organization.chairpersonEmail}
//                   </span>
//                 </div>
//                 <div className="flex items-center gap-2">
//                   <HiOutlinePhone className="h-4 w-4 text-gray-500" />
//                   <span className="text-gray-900 dark:text-white">
//                     {organization.chairpersonPhone}
//                   </span>
//                 </div>
//                 {organization.chairpersonAddress && (
//                   <div className="flex items-start gap-2">
//                     <HiOutlineMapPin className="mt-0.5 h-4 w-4 text-gray-500" />
//                     <span className="text-gray-900 dark:text-white">
//                       {organization.chairpersonAddress}
//                     </span>
//                   </div>
//                 )}
//               </div>
//             </div>

//             {/* Chief Executive */}
//             <div>
//               <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                 Chief Executive
//               </h3>
//               <div className="space-y-2">
//                 <div className="flex items-center gap-2">
//                   <HiOutlineUser className="h-4 w-4 text-gray-500" />
//                   <span className="text-gray-900 dark:text-white">
//                     {organization.chiefExecutiveName}
//                   </span>
//                 </div>
//                 <div className="flex items-center gap-2">
//                   <HiOutlineEnvelope className="h-4 w-4 text-gray-500" />
//                   <span className="text-gray-900 dark:text-white">
//                     {organization.chiefExecutiveEmail}
//                   </span>
//                 </div>
//                 <div className="flex items-center gap-2">
//                   <HiOutlinePhone className="h-4 w-4 text-gray-500" />
//                   <span className="text-gray-900 dark:text-white">
//                     {organization.chiefExecutivePhone}
//                   </span>
//                 </div>
//                 {organization.chiefExecutiveFax && (
//                   <div className="flex items-center gap-2">
//                     <HiOutlinePhone className="h-4 w-4 text-gray-500" />
//                     <span className="text-sm text-gray-600 dark:text-gray-400">
//                       Fax: {organization.chiefExecutiveFax}
//                     </span>
//                   </div>
//                 )}
//                 {organization.chiefExecutiveAddress && (
//                   <div className="flex items-start gap-2">
//                     <HiOutlineMapPin className="mt-0.5 h-4 w-4 text-gray-500" />
//                     <span className="text-gray-900 dark:text-white">
//                       {organization.chiefExecutiveAddress}
//                     </span>
//                   </div>
//                 )}
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Mission, Vision, Values */}
//       {(organization.missionStatement ||
//         organization.visionStatement ||
//         organization.valuesStatement) && (
//         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
//           <div className="mb-4 flex items-center gap-2">
//             <HiOutlineDocumentText className="h-5 w-5 text-blue-600" />
//             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
//               Mission, Vision & Values
//             </h2>
//           </div>

//           <div className="grid gap-6 md:grid-cols-3">
//             {organization.missionStatement && (
//               <div>
//                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                   Mission
//                 </h3>
//                 <p className="text-gray-600 dark:text-gray-400">
//                   {organization.missionStatement}
//                 </p>
//               </div>
//             )}

//             {organization.visionStatement && (
//               <div>
//                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                   Vision
//                 </h3>
//                 <p className="text-gray-600 dark:text-gray-400">
//                   {organization.visionStatement}
//                 </p>
//               </div>
//             )}

//             {organization.valuesStatement && (
//               <div>
//                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                   Values
//                 </h3>
//                 <p className="text-gray-600 dark:text-gray-400">
//                   {organization.valuesStatement}
//                 </p>
//               </div>
//             )}
//           </div>
//         </div>
//       )}

//       {/* Background and Goals */}
//       {(organization.ngoBackground || organization.goals) && (
//         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
//           <div className="mb-4 flex items-center gap-2">
//             <HiOutlineDocumentText className="h-5 w-5 text-blue-600" />
//             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
//               Background & Goals
//             </h2>
//           </div>

//           <div className="grid gap-6 md:grid-cols-2">
//             {organization.ngoBackground && (
//               <div>
//                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                   Background
//                 </h3>
//                 <p className="text-gray-600 dark:text-gray-400">
//                   {organization.ngoBackground}
//                 </p>
//               </div>
//             )}

//             {organization.goals && (
//               <div>
//                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                   Goals
//                 </h3>
//                 <p className="text-gray-600 dark:text-gray-400">
//                   {organization.goals}
//                 </p>
//               </div>
//             )}
//           </div>
//         </div>
//       )}

//       {/* Operations */}
//       {(organization.locationsOfOperation ||
//         organization.sectorsOfOperation ||
//         organization.numberOfEmployees) && (
//         <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
//           <div className="mb-4 flex items-center gap-2">
//             <HiOutlineMapPin className="h-5 w-5 text-blue-600" />
//             <h2 className="text-xl font-bold text-gray-900 dark:text-white">
//               Operations
//             </h2>
//           </div>

//           <div className="grid gap-6 md:grid-cols-3">
//             {organization.locationsOfOperation &&
//               organization.locationsOfOperation.length > 0 && (
//                 <div>
//                   <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                     Locations of Operation
//                   </h3>
//                   <ul className="list-inside list-disc space-y-1 text-gray-600 dark:text-gray-400">
//                     {organization.locationsOfOperation.map(
//                       (location, index) => (
//                         <li key={index}>
//                           {typeof location === "string"
//                             ? location
//                             : `${location.district}, ${location.region}`}
//                         </li>
//                       ),
//                     )}
//                   </ul>
//                 </div>
//               )}

//             {organization.sectorsOfOperation &&
//               organization.sectorsOfOperation.length > 0 && (
//                 <div>
//                   <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                     Sectors of Operation
//                   </h3>
//                   <div className="flex flex-wrap gap-2">
//                     {organization.sectorsOfOperation.map((sector, index) => (
//                       <span
//                         key={index}
//                         className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
//                       >
//                         {sector.name}
//                       </span>
//                     ))}
//                   </div>
//                 </div>
//               )}

//             {organization.numberOfEmployees && (
//               <div>
//                 <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//                   Number of Employees
//                 </h3>
//                 <p className="text-gray-600 dark:text-gray-400">
//                   {organization.numberOfEmployees}
//                 </p>
//               </div>
//             )}
//           </div>
//         </div>
//       )}

//       {/* Documents Section */}
//       <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
//         <div className="mb-4 flex items-center gap-2">
//           <HiOutlineDocumentText className="h-5 w-5 text-blue-600" />
//           <h2 className="text-xl font-bold text-gray-900 dark:text-white">
//             Documents
//           </h2>
//         </div>

//         {documentsLoading ? (
//           <div className="flex items-center justify-center py-8">
//             <div className="h-6 w-6 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
//             <span className="ml-2 text-gray-600 dark:text-gray-400">
//               Loading documents...
//             </span>
//           </div>
//         ) : documents.length > 0 ? (
//           <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
//             {documents.map((document) => (
//               <div
//                 key={document.id}
//                 className="rounded-lg border border-gray-200 p-4 dark:border-gray-700"
//               >
//                 <div className="mb-2 flex items-center justify-between">
//                   <h3 className="text-sm font-medium text-gray-900 dark:text-white">
//                     {document.name}
//                   </h3>
//                   {getStatusIcon(document.status)}
//                 </div>
//                 <p className="mb-2 text-xs text-gray-500 dark:text-gray-400">
//                   Status:{" "}
//                   {document.status.charAt(0).toUpperCase() +
//                     document.status.slice(1)}
//                 </p>
//                 <p className="mb-3 text-xs text-gray-500 dark:text-gray-400">
//                   Uploaded: {formatDate(document.uploadDate)}
//                 </p>
//                 {document.url && (
//                   <button
//                     onClick={() => handleViewDocument(document)}
//                     className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
//                   >
//                     <HiOutlineEye className="h-3 w-3" />
//                     View Document
//                   </button>
//                 )}
//               </div>
//             ))}
//           </div>
//         ) : (
//           <p className="text-gray-600 dark:text-gray-400">
//             No documents available for this organization.
//           </p>
//         )}
//       </div>
//     </div>
//   );
// }

"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import {
  ArrowLeft,
  Building2,
  User,
  Mail,
  Phone,
  MapPin,
  FileText,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Download,
  MoreVertical,
  Loader2,
  Search,
  Filter,
  ChevronDown,
} from "lucide-react";
import { getNGOById, INgo } from "@/services/ngo.services";
import {
  getOrganizationDocuments,
  OrganizationDocument,
} from "@/services/organization-documents.services";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";

export default function OrganizationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const organizationId = params.id as string;

  const [organization, setOrganization] = useState<INgo | null>(null);
  const [documents, setDocuments] = useState<OrganizationDocument[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<
    OrganizationDocument[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [documentsLoading, setDocumentsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocId, setSelectedDocId] = useState<string | null>(null);

  // Document search and filter states
  const [documentSearch, setDocumentSearch] = useState("");
  const [showDocumentFilters, setShowDocumentFilters] = useState(false);
  const [documentFilters, setDocumentFilters] = useState({
    status: [] as string[],
    type: [] as string[],
    required: [] as string[],
  });

  useEffect(() => {
    if (organizationId) {
      fetchOrganizationDetails();
      fetchOrganizationDocuments();
    }
  }, [organizationId]);

  // Filter documents based on search and filters
  useEffect(() => {
    let filtered = documents;

    // Apply search filter
    if (documentSearch.trim()) {
      filtered = filtered.filter(
        (doc) =>
          doc.name.toLowerCase().includes(documentSearch.toLowerCase()) ||
          doc.type.toLowerCase().includes(documentSearch.toLowerCase()),
      );
    }

    // Apply status filter
    if (documentFilters.status.length > 0) {
      filtered = filtered.filter((doc) =>
        documentFilters.status.includes(doc.status),
      );
    }

    // Apply type filter
    if (documentFilters.type.length > 0) {
      filtered = filtered.filter((doc) =>
        documentFilters.type.includes(doc.type),
      );
    }

    // Apply required filter
    if (documentFilters.required.length > 0) {
      filtered = filtered.filter((doc) => {
        const isRequired = doc.required ? "required" : "optional";
        return documentFilters.required.includes(isRequired);
      });
    }

    setFilteredDocuments(filtered);
  }, [documents, documentSearch, documentFilters]);

  const fetchOrganizationDetails = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");
      if (!token) {
        setError("Authentication required");
        return;
      }

      const response = await getNGOById(organizationId, token);
      if (response.status === "success" && response.data) {
        setOrganization(response.data);
      } else {
        setError(response.message || "Failed to fetch organization details");
      }
    } catch (err) {
      setError("An error occurred while fetching organization details");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchOrganizationDocuments = async () => {
    try {
      setDocumentsLoading(true);
      const token = localStorage.getItem("accessToken");
      if (!token) {
        return;
      }

      const response = await getOrganizationDocuments(organizationId, token);
      if (response.status === "success" && response.data) {
        setDocuments(response.data.documents);
        setFilteredDocuments(response.data.documents);
      }
    } catch (err) {
      console.error("Failed to fetch documents:", err);
    } finally {
      setDocumentsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
      case "uploaded":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "rejected":
      case "missing":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
      case "uploaded":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "rejected":
      case "missing":
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  // Document filter helper functions
  const handleDocumentSearch = () => {
    // Search is handled by useEffect
  };

  const handleDocumentFilterChange = (
    filterType: keyof typeof documentFilters,
    value: string,
  ) => {
    setDocumentFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item: string) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearDocumentFilters = () => {
    setDocumentFilters({
      status: [],
      type: [],
      required: [],
    });
    setDocumentSearch("");
  };

  // Document filter options
  const statusOptions = [
    "uploaded",
    "pending",
    "approved",
    "rejected",
    "missing",
  ];
  const typeOptions = [
    "constitution",
    "minutes_of_first_meeting",
    "certificate_from_registrar_general",
    "sworn_in_affidavit",
    "registration_fee",
    "processing_fee",
  ];
  const requiredOptions = ["required", "optional"];

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      const months = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
      ];
      const month = months[date.getMonth()];
      const day = date.getDate().toString().padStart(2, "0");
      const year = date.getFullYear();
      return `${month} ${day}, ${year}`;
    } catch {
      return "N/A";
    }
  };

  const handleViewDocument = (document: OrganizationDocument) => {
    if (document.url) {
      window.open(document.url, "_blank");
    } else {
      alert(`Document "${document.name}" is not available for viewing.`);
    }
  };

  const handleDocumentAction = (
    action: string,
    document: OrganizationDocument,
  ) => {
    setSelectedDocId(null);

    if (action === "View") {
      handleViewDocument(document);
    } else if (action === "Download") {
      if (document.url) {
        window.open(document.url, "_blank");
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">
          Loading organization details...
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
        <p>{error}</p>
        <button
          onClick={() => router.back()}
          className="mt-2 text-sm underline hover:no-underline"
        >
          Go back
        </button>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="py-12 text-center">
        <p className="text-gray-600 dark:text-gray-400">
          Organization not found
        </p>
        <button
          onClick={() => router.back()}
          className="mt-2 text-sm text-blue-600 underline hover:no-underline"
        >
          Go back
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <ArrowLeft className="h-5 w-5" />
            Back
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {organization.name}
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Organization Details
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <span
            className={`inline-flex items-center gap-1 rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(
              organization.approvedStatus || "pending",
            )}`}
          >
            {getStatusIcon(organization.approvedStatus || "pending")}
            {organization.approvedStatus
              ? organization.approvedStatus.charAt(0).toUpperCase() +
                organization.approvedStatus.slice(1)
              : "Pending"}
          </span>
        </div>
      </div>

      {/* Rejection Reason Alert */}
      {organization.approveStatus === "rejected" &&
        organization.rejectionReason && (
          <div className="mb-6 rounded-lg bg-red-50 p-4 dark:bg-red-900/20">
            <div className="flex items-start gap-3">
              <AlertCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-red-600 dark:text-red-400" />
              <div>
                <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
                  Rejection Reason
                </h3>
                <p className="mt-1 text-sm text-red-700 dark:text-red-400">
                  {organization.rejectionReason}
                </p>
              </div>
            </div>
          </div>
        )}

      {/* Organization Details */}
      <div className="mb-6 grid gap-6 lg:grid-cols-2">
        {/* Basic Information */}
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Basic Information
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Organization Name
              </label>
              <p className="mt-1 text-gray-900 dark:text-white">
                {organization.name}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Organization Initials
              </label>
              <p className="mt-1 text-gray-900 dark:text-white">
                {organization.initials}
              </p>
            </div>

            {organization.registrationNumber && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Registration Number
                </label>
                <p className="mt-1 text-gray-900 dark:text-white">
                  {organization.registrationNumber}
                </p>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Type
              </label>
              <p className="mt-1 text-gray-900 dark:text-white">
                {organization.type}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Headquarters Address
              </label>
              <p className="mt-1 text-gray-900 dark:text-white">
                {organization.headquartersAddress}
              </p>
            </div>

            {organization.physicalOfficesAddress && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Physical Office Address
                </label>
                <p className="mt-1 text-gray-900 dark:text-white">
                  {organization.physicalOfficesAddress}
                </p>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Date Founded
              </label>
              <p className="mt-1 text-gray-900 dark:text-white">
                {formatDate(organization.dateFounded)}
              </p>
            </div>

            {organization.dateApprovedByGOM && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Date Approved by Government
                </label>
                <p className="mt-1 text-gray-900 dark:text-white">
                  {formatDate(organization.dateApprovedByGOM)}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Leadership
            </h2>
          </div>

          <div className="space-y-6">
            {/* Chairperson */}
            <div>
              <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                Chairperson
              </h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900 dark:text-white">
                    {organization.chairpersonName}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900 dark:text-white">
                    {organization.chairpersonEmail}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900 dark:text-white">
                    {organization.chairpersonPhone}
                  </span>
                </div>
                {organization.chairpersonAddress && (
                  <div className="flex items-start gap-2">
                    <MapPin className="mt-0.5 h-4 w-4 text-gray-500" />
                    <span className="text-gray-900 dark:text-white">
                      {organization.chairpersonAddress}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Chief Executive */}
            <div>
              <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                Chief Executive
              </h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900 dark:text-white">
                    {organization.chiefExecutiveName}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900 dark:text-white">
                    {organization.chiefExecutiveEmail}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900 dark:text-white">
                    {organization.chiefExecutivePhone}
                  </span>
                </div>
                {organization.chiefExecutiveFax && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Fax: {organization.chiefExecutiveFax}
                    </span>
                  </div>
                )}
                {organization.chiefExecutiveAddress && (
                  <div className="flex items-start gap-2">
                    <MapPin className="mt-0.5 h-4 w-4 text-gray-500" />
                    <span className="text-gray-900 dark:text-white">
                      {organization.chiefExecutiveAddress}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mission, Vision, Values */}
      {(organization.missionStatement ||
        organization.visionStatement ||
        organization.valuesStatement) && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Mission, Vision & Values
            </h2>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            {organization.missionStatement && (
              <div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                  Mission
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {organization.missionStatement}
                </p>
              </div>
            )}

            {organization.visionStatement && (
              <div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                  Vision
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {organization.visionStatement}
                </p>
              </div>
            )}

            {organization.valuesStatement && (
              <div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                  Values
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {organization.valuesStatement}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Background and Goals */}
      {(organization.ngoBackground || organization.goals) && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Background & Goals
            </h2>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {organization.ngoBackground && (
              <div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                  Background
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {organization.ngoBackground}
                </p>
              </div>
            )}

            {organization.goals && (
              <div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                  Goals
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {organization.goals}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Operations */}
      {(organization.locationsOfOperation ||
        organization.sectorsOfOperation ||
        organization.numberOfEmployees) && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <MapPin className="h-5 w-5 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Operations
            </h2>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            {organization.locationsOfOperation &&
              organization.locationsOfOperation.length > 0 && (
                <div>
                  <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                    Locations of Operation
                  </h3>
                  <ul className="list-inside list-disc space-y-1 text-gray-600 dark:text-gray-400">
                    {organization.locationsOfOperation.map(
                      (location, index) => (
                        <li key={index}>
                          {typeof location === "string"
                            ? location
                            : `${location.district}, ${location.region}`}
                        </li>
                      ),
                    )}
                  </ul>
                </div>
              )}

            {organization.sectorsOfOperation &&
              organization.sectorsOfOperation.length > 0 && (
                <div>
                  <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                    Sectors of Operation
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {organization.sectorsOfOperation.map((sector, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                      >
                        {typeof sector === "string" ? sector : sector.name}
                      </span>
                    ))}
                  </div>
                </div>
              )}

            {organization.numberOfEmployees && (
              <div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                  Number of Employees
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {organization.numberOfEmployees}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Documents Section - Table Format */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div className="border-b border-gray-200 p-6 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Documents
              </h2>
              <span className="ml-2 rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600 dark:bg-gray-700 dark:text-gray-400">
                {filteredDocuments.length} of {documents.length}
              </span>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="mt-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex flex-1 items-center gap-2">
              <div className="relative max-w-md flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search documents..."
                  value={documentSearch}
                  onChange={(e) => setDocumentSearch(e.target.value)}
                  onKeyPress={(e) =>
                    e.key === "Enter" && handleDocumentSearch()
                  }
                  className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                />
              </div>
              <button
                onClick={handleDocumentSearch}
                className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Search
              </button>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowDocumentFilters(!showDocumentFilters)}
                className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                <Filter className="h-4 w-4" />
                Filters
                <ChevronDown
                  className={`h-4 w-4 transition-transform ${showDocumentFilters ? "rotate-180" : ""}`}
                />
              </button>
            </div>
          </div>

          {/* Filters Panel */}
          {showDocumentFilters && (
            <div className="mt-4 rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-600 dark:bg-gray-700">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Filters
                </h3>
                <button
                  onClick={clearDocumentFilters}
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400"
                >
                  Clear All
                </button>
              </div>

              <div className="grid gap-4 sm:grid-cols-3">
                {/* Status Filter */}
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Status
                  </label>
                  <div className="space-y-2">
                    {statusOptions.map((status) => (
                      <label key={status} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={documentFilters.status.includes(status)}
                          onChange={() =>
                            handleDocumentFilterChange("status", status)
                          }
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
                          {status.replace(/_/g, " ")}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Type Filter */}
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Document Type
                  </label>
                  <div className="space-y-2">
                    {typeOptions.map((type) => (
                      <label key={type} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={documentFilters.type.includes(type)}
                          onChange={() =>
                            handleDocumentFilterChange("type", type)
                          }
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
                          {type.replace(/_/g, " ")}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Required Filter */}
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Required Status
                  </label>
                  <div className="space-y-2">
                    {requiredOptions.map((required) => (
                      <label key={required} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={documentFilters.required.includes(required)}
                          onChange={() =>
                            handleDocumentFilterChange("required", required)
                          }
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm capitalize text-gray-700 dark:text-gray-300">
                          {required}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="overflow-x-auto">
          {documentsLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                Loading documents...
              </span>
            </div>
          ) : (
            <Table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <TableHeader>
                <TableRow className="bg-gray-50 dark:bg-gray-800">
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Document Name
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Status
                  </TableHead>
                  <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Upload Date
                  </TableHead>
                  <TableHead className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                {filteredDocuments.length > 0 ? (
                  filteredDocuments.map((document) => (
                    <TableRow
                      key={document.id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    >
                      <TableCell className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 dark:text-white">
                        {document.name}
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4">
                        <span
                          className={`inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(
                            document.status,
                          )}`}
                        >
                          {getStatusIcon(document.status)}
                          {document.status.charAt(0).toUpperCase() +
                            document.status.slice(1)}
                        </span>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4 text-gray-600 dark:text-gray-400">
                        {formatDate(document.uploadDate)}
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-6 py-4 text-right">
                        <div className="relative inline-block text-left">
                          <div>
                            <button
                              type="button"
                              onClick={() =>
                                setSelectedDocId(
                                  selectedDocId === document.id
                                    ? null
                                    : document.id,
                                )
                              }
                              className="inline-flex items-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600 focus:outline-none dark:hover:bg-gray-700 dark:hover:text-gray-300"
                            >
                              <MoreVertical className="h-5 w-5" />
                            </button>
                          </div>

                          {selectedDocId === document.id && (
                            <div className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800">
                              <div className="py-1">
                                <button
                                  onClick={() =>
                                    handleDocumentAction("View", document)
                                  }
                                  className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                >
                                  <Eye className="mr-3 h-4 w-4" />
                                  View Document
                                </button>
                                <button
                                  onClick={() =>
                                    handleDocumentAction("Download", document)
                                  }
                                  className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                >
                                  <Download className="mr-3 h-4 w-4" />
                                  Download
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={4}
                      className="py-12 text-center text-gray-500"
                    >
                      <div className="flex flex-col items-center justify-center gap-2">
                        <FileText className="h-8 w-8 text-gray-400" />
                        <p className="text-lg font-medium">
                          {documents.length === 0
                            ? "No documents found"
                            : "No matching documents"}
                        </p>
                        <p className="text-sm text-gray-500">
                          {documents.length === 0
                            ? "No documents available for this organization"
                            : "Try adjusting your search or filters"}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </div>
    </div>
  );
}
