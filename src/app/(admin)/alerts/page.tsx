"use client";
import React, { useState, useEffect } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Modal } from "@/components/ui/modal";
import {
  SearchIcon,
  PlusIcon,
  EyeIcon,
  Trash2,
  FilterIcon,
  XIcon,
  Mail,
  Smartphone,
  Info,
  AlertTriangle,
  AlertCircle,
} from "lucide-react";
import { format } from "date-fns";

type AlertType = "info" | "warning" | "error";

const initialAlerts = [
  {
    id: 1,
    type: "info" as AlertType,
    message: "Your profile was updated successfully.",
    date: "2024-06-01",
    viaEmail: true,
    viaSMS: false,
    user: "<PERSON>",
  },
  {
    id: 2,
    type: "warning" as AlertType,
    message: "Password will expire in 5 days.",
    date: "2024-06-02",
    viaEmail: true,
    viaSMS: true,
    user: "<PERSON>",
  },
  {
    id: 3,
    type: "error" as AlertType,
    message: "Failed login attempt detected.",
    date: "2024-06-03",
    viaEmail: false,
    viaSMS: true,
  },
  {
    id: 4,
    type: "info" as AlertType,
    message: "A new device was added to your account.",
    date: "2024-06-04",
    viaEmail: true,
    viaSMS: false,
    user: "Carol Lee",
  },
  {
    id: 5,
    type: "warning" as AlertType,
    message: "Unusual activity detected in your account.",
    date: "2024-06-05",
    viaEmail: true,
    viaSMS: true,
  },
];

const users = [
  { id: 1, name: "Alice Smith", email: "<EMAIL>" },
  { id: 2, name: "Bob Johnson", email: "<EMAIL>" },
  { id: 3, name: "Carol Lee", email: "<EMAIL>" },
  { id: 4, name: "David Wilson", email: "<EMAIL>" },
  { id: 5, name: "Emma Davis", email: "<EMAIL>" },
];

const typeStyles: Record<AlertType, string> = {
  info: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
  warning:
    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  error: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
};

const typeIcons: Record<AlertType, React.ReactNode> = {
  info: <Info className="h-4 w-4" />,
  warning: <AlertTriangle className="h-4 w-4" />,
  error: <AlertCircle className="h-4 w-4" />,
};

const deliveryMethodOptions = ["email", "sms", "in-app"];
const userOptions = users.map((user) => user.name);
const typeOptions = ["info", "warning", "error"] as AlertType[];

export default function UserAlertsPage() {
  const [mounted, setMounted] = useState(false);
  const [alerts, setAlerts] = useState(initialAlerts);
  const [showAddModal, setShowAddModal] = useState(false);
  const [viewingAlert, setViewingAlert] = useState<
    (typeof initialAlerts)[0] | null
  >(null);
  const [search, setSearch] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    type: [] as AlertType[],
    deliveryMethod: [] as string[],
    user: [] as string[],
  });
  const [form, setForm] = useState({
    type: "info" as AlertType,
    message: "",
    viaEmail: false,
    viaSMS: false,
    user: "",
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleFilter = (
    filterType: keyof typeof filters,
    value: string | AlertType,
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: (prev[filterType] as any[]).includes(value)
        ? (prev[filterType] as any[]).filter((item) => item !== value)
        : [...(prev[filterType] as any[]), value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      type: [],
      deliveryMethod: [],
      user: [],
    });
  };

  const filtered = alerts.filter((alert) => {
    const matchesSearch = Object.values(alert).some((val) =>
      String(val).toLowerCase().includes(search.toLowerCase()),
    );
    const matchesType =
      filters.type.length === 0 || filters.type.includes(alert.type);
    const matchesDelivery =
      filters.deliveryMethod.length === 0 ||
      (filters.deliveryMethod.includes("email") && alert.viaEmail) ||
      (filters.deliveryMethod.includes("sms") && alert.viaSMS) ||
      (filters.deliveryMethod.includes("in-app") &&
        !alert.viaEmail &&
        !alert.viaSMS);
    const matchesUser =
      filters.user.length === 0 ||
      (alert.user && filters.user.includes(alert.user)) ||
      (!alert.user && filters.user.includes("All Users"));

    return matchesSearch && matchesType && matchesDelivery && matchesUser;
  });

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value, type } = e.target;
    if (type === "checkbox") {
      setForm((prev) => ({
        ...prev,
        [name]: (e.target as HTMLInputElement).checked,
      }));
    } else {
      setForm((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newAlert = {
      id: alerts.length + 1,
      type: form.type,
      message: form.message,
      date: new Date().toISOString().slice(0, 10),
      viaEmail: form.viaEmail,
      viaSMS: form.viaSMS,
      user: form.user || undefined,
    };
    setAlerts([newAlert, ...alerts]);
    setShowAddModal(false);
    setForm({
      type: "info",
      message: "",
      viaEmail: false,
      viaSMS: false,
      user: "",
    });
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch (error) {
      return dateString;
    }
  };

  const handleView = (alert: (typeof initialAlerts)[0]) => {
    setViewingAlert(alert);
  };

  const handleDelete = (alertId: number) => {
    if (confirm("Are you sure you want to delete this alert?")) {
      setAlerts(alerts.filter((a) => a.id !== alertId));
    }
  };

  if (!mounted) {
    return (
      <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
        <div className="flex h-64 items-center justify-center">
          <div className="text-gray-500">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      <div className="mb-8 flex flex-col justify-between gap-6 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            User Alerts
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage and view all system alerts for users
          </p>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="Search alerts..."
              className="w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <FilterIcon className="h-4 w-4" />
              Filters
              {Object.values(filters).some((arr) => arr.length > 0) && (
                <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {Object.values(filters).flat().length}
                </span>
              )}
            </button>

            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <PlusIcon className="h-4 w-4" />
              Add Alert
            </button>
          </div>
        </div>
      </div>

      {showFilters && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <div className="flex gap-2">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <XIcon className="h-4 w-4" />
                Clear all
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              >
                <XIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-3">
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Alert Type
              </h4>
              <div className="space-y-2">
                {typeOptions.map((type) => (
                  <div key={type} className="flex items-center">
                    <input
                      id={`type-${type}`}
                      type="checkbox"
                      checked={filters.type.includes(type)}
                      onChange={() => toggleFilter("type", type)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`type-${type}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Delivery Method
              </h4>
              <div className="space-y-2">
                {deliveryMethodOptions.map((method) => (
                  <div key={method} className="flex items-center">
                    <input
                      id={`method-${method}`}
                      type="checkbox"
                      checked={filters.deliveryMethod.includes(method)}
                      onChange={() => toggleFilter("deliveryMethod", method)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`method-${method}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {method.charAt(0).toUpperCase() + method.slice(1)}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {Object.values(filters).some((arr) => arr.length > 0) && (
        <div className="mb-4 flex flex-wrap gap-2">
          {filters.type.map((type) => (
            <span
              key={`type-${type}`}
              className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              Type: {type.charAt(0).toUpperCase() + type.slice(1)}
              <button
                onClick={() => toggleFilter("type", type)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-800 hover:bg-blue-200 dark:text-blue-200 dark:hover:bg-blue-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
          {filters.deliveryMethod.map((method) => (
            <span
              key={`method-${method}`}
              className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              Method: {method.charAt(0).toUpperCase() + method.slice(1)}
              <button
                onClick={() => toggleFilter("deliveryMethod", method)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-800 hover:bg-green-200 dark:text-green-200 dark:hover:bg-green-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
          {filters.user.map((user) => (
            <span
              key={`user-${user}`}
              className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              User: {user}
              <button
                onClick={() => toggleFilter("user", user)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-800 hover:bg-blue-200 dark:text-blue-200 dark:hover:bg-blue-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm dark:border-gray-700">
        <div className="overflow-x-auto">
          <Table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <TableHeader>
              <TableRow className="bg-gray-50 dark:bg-gray-800">
                <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                  ID
                </TableHead>
                <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                  Type
                </TableHead>
                <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                  Message
                </TableHead>
                <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                  User
                </TableHead>
                <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                  Date
                </TableHead>
                <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                  Delivery
                </TableHead>
                <TableHead className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700 dark:text-gray-300">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
              {filtered.map((alert) => (
                <TableRow
                  key={alert.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                >
                  <TableCell className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 dark:text-white">
                    #{alert.id}
                  </TableCell>
                  <TableCell className="whitespace-nowrap px-6 py-4">
                    <div className="flex items-center gap-2">
                      {typeIcons[alert.type]}
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${typeStyles[alert.type]}`}
                      >
                        {alert.type.charAt(0).toUpperCase() +
                          alert.type.slice(1)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4 text-gray-600 dark:text-gray-400">
                    <div className="max-w-xs truncate">{alert.message}</div>
                  </TableCell>
                  <TableCell className="whitespace-nowrap px-6 py-4 text-gray-600 dark:text-gray-400">
                    {alert.user || "All Users"}
                  </TableCell>
                  <TableCell className="whitespace-nowrap px-6 py-4 text-gray-600 dark:text-gray-400">
                    {formatDate(alert.date)}
                  </TableCell>
                  <TableCell className="whitespace-nowrap px-6 py-4">
                    <div className="flex gap-2">
                      {alert.viaEmail && (
                        <span className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          <Mail className="h-3 w-3" />
                          Email
                        </span>
                      )}
                      {alert.viaSMS && (
                        <span className="inline-flex items-center gap-1 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                          <Smartphone className="h-3 w-3" />
                          SMS
                        </span>
                      )}
                      {!alert.viaEmail && !alert.viaSMS && (
                        <span className="inline-flex items-center gap-1 rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          In-app
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="whitespace-nowrap px-6 py-4 text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleView(alert)}
                        className="inline-flex items-center rounded-md bg-blue-100 p-2 text-blue-600 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-blue-900 dark:text-blue-400 dark:hover:bg-blue-800"
                        title="View details"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(alert.id)}
                        className="inline-flex items-center rounded-md bg-red-100 p-2 text-red-600 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-red-900 dark:text-red-400 dark:hover:bg-red-800"
                        title="Delete alert"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
              {filtered.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={7}
                    className="py-12 text-center text-gray-500"
                  >
                    <div className="flex flex-col items-center justify-center gap-2">
                      <SearchIcon className="h-8 w-8 text-gray-400" />
                      <p className="text-lg font-medium">No alerts found</p>
                      <p className="text-sm text-gray-500">
                        Try adjusting your search or filters
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Alert"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              User
            </label>
            <select
              name="user"
              value={form.user}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            >
              <option value="">All Users</option>
              {users.map((user) => (
                <option key={user.id} value={user.name}>
                  {user.name} ({user.email})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Alert Type
            </label>
            <select
              name="type"
              value={form.type}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            >
              <option value="info">Info</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
            </select>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Message *
            </label>
            <textarea
              name="message"
              value={form.message}
              onChange={handleChange}
              required
              rows={3}
              className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>

          <div className="flex gap-4">
            <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
              <input
                type="checkbox"
                name="viaEmail"
                checked={form.viaEmail}
                onChange={handleChange}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
              />
              Send via Email
            </label>
            <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
              <input
                type="checkbox"
                name="viaSMS"
                checked={form.viaSMS}
                onChange={handleChange}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
              />
              Send via SMS
            </label>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => setShowAddModal(false)}
              className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Add Alert
            </button>
          </div>
        </form>
      </Modal>

      <Modal
        isOpen={!!viewingAlert}
        onClose={() => setViewingAlert(null)}
        title="Alert Details"
      >
        {viewingAlert && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {typeIcons[viewingAlert.type]}
                <span
                  className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${typeStyles[viewingAlert.type]}`}
                >
                  {viewingAlert.type.charAt(0).toUpperCase() +
                    viewingAlert.type.slice(1)}
                </span>
              </div>
              <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                Delivered
              </span>
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
                  Alert ID
                </label>
                <p className="text-sm font-semibold text-gray-900 dark:text-white">
                  #{viewingAlert.id}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
                  Message
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {viewingAlert.message}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
                  User
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {viewingAlert.user || "All Users"}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
                  Date
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {formatDate(viewingAlert.date)}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
                  Delivery Method
                </label>
                <div className="flex gap-2">
                  {viewingAlert.viaEmail && (
                    <span className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      <Mail className="h-3 w-3" />
                      Email
                    </span>
                  )}
                  {viewingAlert.viaSMS && (
                    <span className="inline-flex items-center gap-1 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                      <Smartphone className="h-3 w-3" />
                      SMS
                    </span>
                  )}
                  {!viewingAlert.viaEmail && !viewingAlert.viaSMS && (
                    <span className="inline-flex items-center gap-1 rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                      In-app
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end pt-4">
              <button
                type="button"
                onClick={() => setViewingAlert(null)}
                className="rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}
