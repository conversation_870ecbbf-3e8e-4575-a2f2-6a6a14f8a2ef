"use client";
import React, { useState, useEffect } from "react";
import { HiOutlineLightBulb } from "react-icons/hi2";
import {
  SearchIcon,
  PlusIcon,
  EyeIcon,
  MoreVertical,
  Edit2,
  Trash2,
  FilterIcon,
  XIcon,
} from "lucide-react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import {
  getAllSectors,
  createSector,
  updateSector,
  deleteSector,
  AreasOfFocus,
} from "@/services/area-of-focus.services";
import { format } from "date-fns";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import { Modal } from "@/components/ui/modal";

const statusOptions = ["active", "inactive"];
const categoryOptions = [
  "Health",
  "Education",
  "Agriculture",
  "Water & Sanitation",
  "Infrastructure",
  "Environment",
  "Economic Development",
  "Social Services",
];

export default function FocusAreasPage() {
  const [focusAreas, setFocusAreas] = useState<AreasOfFocus[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedArea, setSelectedArea] = useState<string | null>(null);

  // for editing the focus area
  const [editOpen, setEditOpen] = useState(false);
  const [editingArea, setEditingArea] = useState<AreasOfFocus | null>(null);

  const [filters, setFilters] = useState({
    status: [] as string[],
    category: [] as string[],
  });
  const [form, setForm] = useState({
    name: "",
    description: "",
    category: "",
    status: "active",
  });

  useEffect(() => {
    fetchSectors();
  }, []);

  const fetchSectors = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");
      const response = await getAllSectors(undefined, token || undefined);
      if (response.status === "success" && response.data) {
        setFocusAreas(response.data);
      } else {
        setError(response.message || "Failed to fetch focus areas");
      }
    } catch (err) {
      setError("An error occurred while fetching focus areas");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem("accessToken");
      const response = await createSector(
        {
          name: form.name,
          description: form.description,
          category: form.category,
          status: form.status,
        },
        token || undefined,
      );

      if (response.status === "success" && response.data) {
        setFocusAreas([...focusAreas, response.data]);
        setShowModal(false);
        setForm({
          name: "",
          description: "",
          category: "",
          status: "active",
        });
      } else {
        setError(response.message || "Failed to create focus area");
      }
    } catch (err) {
      setError("An error occurred while creating the focus area");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const toggleFilter = (filterType: keyof typeof filters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: [],
      category: [],
    });
  };

  const filtered = focusAreas.filter((area) => {
    const matchesSearch =
      area.name.toLowerCase().includes(search.toLowerCase()) ||
      area.description?.toLowerCase().includes(search.toLowerCase()) ||
      area.category?.toLowerCase().includes(search.toLowerCase());
    const matchesStatus =
      filters.status.length === 0 ||
      filters.status.includes(area.status || "active");
    const matchesCategory =
      filters.category.length === 0 ||
      filters.category.includes(area.category || "");

    return matchesSearch && matchesStatus && matchesCategory;
  });

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  const handleAction = async (action: string, id: string) => {
    if (action === "Delete") {
      if (window.confirm("Are you sure you want to delete this focus area?")) {
        try {
          setLoading(true);
          const token = localStorage.getItem("accessToken");
          const response = await deleteSector(id, token || undefined);

          if (response.status === "success") {
            setFocusAreas(focusAreas.filter((area) => area._id !== id));
          } else {
            setError(response.message || "Failed to delete focus area");
          }
        } catch (err) {
          setError("An error occurred while deleting the focus area");
          console.error(err);
        } finally {
          setLoading(false);
        }
      }
    }
  };

  const handleEditSubmit = async () => {
    if (!editingArea || !editingArea._id) return;

    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");
      const response = await updateSector(
        editingArea._id,
        {
          name: editingArea.name,
          description: editingArea.description,
          category: editingArea.category,
          status: editingArea.status,
        },
        token || undefined,
      );

      if (response.status === "success" && response.data) {
        setFocusAreas(
          focusAreas.map((area) =>
            area._id === editingArea._id ? response.data! : area,
          ),
        );
        setEditOpen(false);
        setEditingArea(null);
      } else {
        setError(response.message || "Failed to update focus area");
      }
    } catch (err) {
      setError("An error occurred while updating the focus area");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch {
      return "N/A";
    }
  };

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      {error && (
        <div className="mb-6 rounded-lg bg-red-100 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
          {error}
          <button
            onClick={() => setError(null)}
            className="ml-2 text-sm underline hover:no-underline"
          >
            Dismiss
          </button>
        </div>
      )}

      <div className="mb-8 flex flex-col justify-between gap-6 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Focus Areas
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage sectors and areas of focus ({focusAreas.length} total)
          </p>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="Search focus areas..."
              className="w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <FilterIcon className="h-4 w-4" />
              Filters
              {Object.values(filters).some((arr) => arr.length > 0) && (
                <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {Object.values(filters).flat().length}
                </span>
              )}
            </button>

            <button
              onClick={() => setShowModal(true)}
              className="flex items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <PlusIcon className="h-4 w-4" />
              Add Focus Area
            </button>
          </div>
        </div>
      </div>

      {showFilters && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <div className="flex gap-2">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <XIcon className="h-4 w-4" />
                Clear all
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              >
                <XIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </h4>
              <div className="space-y-2">
                {statusOptions.map((status) => (
                  <div key={`status-${status}`} className="flex items-center">
                    <input
                      id={`status-${status}`}
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={() => toggleFilter("status", status)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`status-${status}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Category
              </h4>
              <div className="space-y-2">
                {categoryOptions.map((category) => (
                  <div
                    key={`category-${category}`}
                    className="flex items-center"
                  >
                    <input
                      id={`category-${category}`}
                      type="checkbox"
                      checked={filters.category.includes(category)}
                      onChange={() => toggleFilter("category", category)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`category-${category}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {category}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {Object.values(filters).some((arr) => arr.length > 0) && (
        <div className="mb-4 flex flex-wrap gap-2">
          {filters.status.map((status) => (
            <span
              key={`status-tag-${status}`}
              className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              Status: {status.charAt(0).toUpperCase() + status.slice(1)}
              <button
                onClick={() => toggleFilter("status", status)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-800 hover:bg-blue-200 dark:text-blue-200 dark:hover:bg-blue-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
          {filters.category.map((category) => (
            <span
              key={`category-tag-${category}`}
              className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              Category: {category}
              <button
                onClick={() => toggleFilter("category", category)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-800 hover:bg-green-200 dark:text-green-200 dark:hover:bg-green-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      <div className="mt-4 space-y-4">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">
              Loading focus areas...
            </span>
          </div>
        ) : filtered.length > 0 ? (
          filtered.map((area) => (
            <div
              key={area._id}
              className="rounded-lg border border-gray-200 bg-white p-5 shadow-sm transition hover:shadow-md dark:border-gray-700 dark:bg-gray-900"
            >
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/40">
                    <HiOutlineLightBulb className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {area.name}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      {area.description || "No description available"}
                    </p>
                  </div>
                </div>

                <div className="mt-4 flex sm:mt-0">
                  {/* <button
                    onClick={() => handleAction("View", area._id!)}
                    className="flex items-center rounded-md px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button> */}
                  <button
                    onClick={() => {
                      setEditingArea(area);
                      setEditOpen(true);
                    }}
                    className="flex items-center rounded-md px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800"
                  >
                    <Edit2 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleAction("Delete", area._id!)}
                    className="flex items-center rounded-md px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-800/20"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="mt-4 flex flex-wrap items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
                <span className="inline-flex items-center gap-1 rounded-full border border-gray-300 bg-gray-50 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300">
                  <FilterIcon className="h-3 w-3" />{" "}
                  {area.category || "Uncategorized"}
                </span>

                <span
                  className={`inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(
                    area.status || "active",
                  )}`}
                >
                  {area.status
                    ? area.status.charAt(0).toUpperCase() + area.status.slice(1)
                    : "Active"}
                </span>

                <span className="inline-flex items-center gap-1 text-xs">
                  <HiOutlineLightBulb className="h-3 w-3" />
                  <strong>{area.organizationCount || 0}</strong> Organizations
                </span>

                <span className="inline-flex items-center gap-1 text-xs">
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    Created:
                  </span>{" "}
                  {formatDate(area.createdAt)}
                </span>
              </div>
            </div>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center py-16">
            <HiOutlineLightBulb className="h-12 w-12 text-gray-400" />
            <p className="mt-2 text-lg font-medium text-gray-700 dark:text-gray-300">
              No focus areas found
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Try adjusting your search or filters
            </p>
          </div>
        )}
      </div>

      {/* Add Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 p-4 backdrop-blur-sm">
          <div className="w-full max-w-md rounded-lg border border-gray-200 bg-white p-6 shadow-2xl dark:border-gray-700 dark:bg-gray-800">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Add Focus Area
              </h2>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XIcon className="h-6 w-6" />
              </button>
            </div>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-200">
                  Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={form.name}
                  onChange={handleChange}
                  required
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm text-black transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-200">
                  Category
                </label>
                <select
                  name="category"
                  value={form.category}
                  onChange={handleChange}
                  required
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm text-black transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select a category</option>
                  {categoryOptions.map((cat) => (
                    <option key={cat} value={cat}>
                      {cat}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-200">
                  Description
                </label>
                <textarea
                  name="description"
                  value={form.description}
                  onChange={handleChange}
                  required
                  rows={3}
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm text-black transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-200">
                  Status
                </label>
                <select
                  name="status"
                  value={form.status}
                  onChange={handleChange}
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm text-black transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div className="mt-6 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="rounded-lg border border-gray-300 bg-white px-5 py-2.5 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="rounded-lg bg-blue-600 px-5 py-2.5 text-sm font-medium text-white transition-colors hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
                  disabled={loading}
                >
                  {loading ? "Adding..." : "Add Focus Area"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <Modal
        isOpen={editOpen}
        onClose={() => {
          setEditOpen(false);
          setEditingArea(null);
        }}
        title="Edit Focus Area"
      >
        {editingArea && (
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleEditSubmit();
            }}
            className="space-y-5"
          >
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Name
              </label>
              <input
                type="text"
                value={editingArea.name}
                onChange={(e) =>
                  setEditingArea({ ...editingArea, name: e.target.value })
                }
                className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2 outline-none focus:border-primary dark:border-gray-600 dark:text-white"
                required
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Category
              </label>
              <select
                value={editingArea.category || ""}
                onChange={(e) =>
                  setEditingArea({ ...editingArea, category: e.target.value })
                }
                className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2 outline-none focus:border-primary dark:border-gray-600 dark:text-white"
              >
                <option value="">Select a category</option>
                {categoryOptions.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>
            </div>

            <TextAreaGroup
              label="Description"
              placeholder="Enter focus area description"
              value={editingArea.description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                setEditingArea({ ...editingArea, description: e.target.value })
              }
            />

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </label>
              <select
                value={editingArea.status || "active"}
                onChange={(e) =>
                  setEditingArea({ ...editingArea, status: e.target.value })
                }
                className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2 outline-none focus:border-primary dark:border-gray-600 dark:text-white"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div className="flex justify-end gap-3">
              <button
                type="button"
                onClick={() => {
                  setEditOpen(false);
                  setEditingArea(null);
                }}
                className="rounded-lg border border-gray-400 px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="hover:bg-primary-dark rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white"
              >
                Save Changes
              </button>
            </div>
          </form>
        )}
      </Modal>
    </div>
  );
}
