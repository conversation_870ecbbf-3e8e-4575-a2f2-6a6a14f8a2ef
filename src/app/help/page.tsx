"use client";
import React, { useState, useEffect } from "react";
import {
  HiOutlineQuestionMarkCircle,
  HiOutlinePhone,
  HiOutlineChatBubbleLeftRight,
  HiOutlineDocumentText,
} from "react-icons/hi2";
import { HiOutlineMail } from "react-icons/hi";
import { getFaqs, Faq } from "@/services/faq.services";

// Fallback FAQs in case API fails
const fallbackFaqs = [
  {
    id: 1,
    question: "How do I register my organization?",
    answer:
      "To register your organization, navigate to the Organizations section and click on 'Add New Organization'. Fill in all required fields including organization details, contact information, and supporting documents. Submit the form and wait for approval from our team.",
  },
  {
    id: 2,
    question: "How long does the approval process take?",
    answer:
      "The approval process typically takes 3-5 business days. You will receive email notifications at each stage of the process. If you haven't heard back within a week, please contact our support team.",
  },
  {
    id: 3,
    question: "How can I update my organization's information?",
    answer:
      "You can update your organization's information by going to the Organizations section, finding your organization, and clicking the 'Edit' button. Make your changes and submit the updated information for review.",
  },
  {
    id: 4,
    question: "What documents are required for registration?",
    answer:
      "Required documents include: Certificate of Registration, Tax Exemption Certificate, Board Resolution, Constitution/By-laws, and Annual Reports. All documents should be in PDF format and clearly legible.",
  },
  {
    id: 5,
    question: "How do I reset my password?",
    answer:
      "To reset your password, go to the login page and click 'Forgot Password'. Enter your email address and follow the instructions sent to your email. Make sure to check your spam folder if you don't receive the email.",
  },
  {
    id: 6,
    question: "Can I have multiple users for my organization?",
    answer:
      "Yes, you can have multiple users for your organization. The primary administrator can add additional users through the User Management section. Each user will have different permission levels based on their role.",
  },
];

const supportChannels = [
  {
    title: "Email Support",
    description: "Get help via email",
    icon: HiOutlineMail,
    contact: "<EMAIL>",
    response: "Within 24 hours",
  },
  {
    title: "Phone Support",
    description: "Call us directly",
    icon: HiOutlinePhone,
    contact: "+265 1 234 567",
    response: "Mon-Fri, 8AM-5PM",
  },
  {
    title: "Live Chat",
    description: "Chat with our team",
    icon: HiOutlineChatBubbleLeftRight,
    contact: "Available on website",
    response: "Real-time support",
  },
  {
    title: "Documentation",
    description: "Browse our guides",
    icon: HiOutlineDocumentText,
    contact: "User Manuals",
    response: "Self-service help",
  },
];

export default function HelpPage() {
  const [openFaq, setOpenFaq] = useState<number | null>(null);
  const [faqs, setFaqs] = useState<Faq[]>([]);
  const [faqsLoading, setFaqsLoading] = useState(true);

  useEffect(() => {
    const fetchFaqs = async () => {
      try {
        setFaqsLoading(true);
        const response = await getFaqs();
        if (
          response.status === "success" &&
          response.data &&
          response.data.length > 0
        ) {
          setFaqs(response.data);
        } else {
          // Use fallback FAQs if API returns no data
          setFaqs(fallbackFaqs as any);
        }
      } catch (error) {
        console.error("Failed to fetch FAQs:", error);
        // Use fallback FAQs if API fails
        setFaqs(fallbackFaqs as any);
      } finally {
        setFaqsLoading(false);
      }
    };

    fetchFaqs();
  }, []);

  return (
    <div className="mt-4 w-full">
      <h1 className="mb-6 text-3xl font-extrabold tracking-tight text-primary">
        Help Center
      </h1>

      {/* Support Channels */}
      <div className="mb-8">
        <h2 className="mb-4 text-2xl font-bold text-dark dark:text-white">
          Get Support
        </h2>
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {supportChannels.map((channel, index) => (
            <div
              key={index}
              className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark"
            >
              <div className="mb-4 flex size-12 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
                <channel.icon className="size-6 text-primary" />
              </div>
              <h3 className="mb-2 text-lg font-semibold text-dark dark:text-white">
                {channel.title}
              </h3>
              <p className="mb-3 text-sm text-gray-600 dark:text-gray-300">
                {channel.description}
              </p>
              <div className="text-sm">
                <p className="font-medium text-primary">{channel.contact}</p>
                <p className="text-gray-500 dark:text-gray-400">
                  {channel.response}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* FAQs */}
      <div>
        <div className="mb-4 flex items-center gap-3">
          <div className="flex size-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
            <HiOutlineQuestionMarkCircle className="size-6 text-primary" />
          </div>
          <h2 className="text-2xl font-bold text-dark dark:text-white">
            Frequently Asked Questions
          </h2>
        </div>

        <div className="space-y-4">
          {faqsLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                Loading FAQs...
              </span>
            </div>
          ) : (
            faqs.map((faq, index) => (
              <div
                key={faq._id || faq.id || index}
                className="rounded-xl bg-white shadow-lg dark:bg-gray-dark"
              >
                <button
                  className="flex w-full items-center justify-between px-6 py-4 text-left focus:outline-none"
                  onClick={() => setOpenFaq(openFaq === index ? null : index)}
                >
                  <span className="text-lg font-semibold text-primary">
                    {faq.question}
                  </span>
                  <span
                    className={`transition-transform ${openFaq === index ? "rotate-180" : "rotate-0"}`}
                  >
                    ▼
                  </span>
                </button>
                {openFaq === index && (
                  <div className="border-t border-gray-200 px-6 pb-4 pt-2 dark:border-dark-3">
                    <p className="text-gray-600 dark:text-gray-300">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Contact Form */}
      <div className="mt-8 rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <h2 className="mb-4 text-2xl font-bold text-dark dark:text-white">
          Still Need Help?
        </h2>
        <p className="mb-4 text-gray-600 dark:text-gray-300">
          Can&apos;t find what you&apos;re looking for? Send us a message and
          we&apos;ll get back to you as soon as possible.
        </p>

        <div className="grid gap-4 sm:grid-cols-2">
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Name
            </label>
            <input
              type="text"
              className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              placeholder="Your full name"
            />
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Email
            </label>
            <input
              type="email"
              className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div className="mt-4">
          <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
            Subject
          </label>
          <input
            type="text"
            className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
            placeholder="What can we help you with?"
          />
        </div>

        <div className="mt-4">
          <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
            Message
          </label>
          <textarea
            rows={4}
            className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
            placeholder="Please describe your issue in detail..."
          />
        </div>

        <button className="mt-4 rounded bg-primary px-6 py-2 text-white transition hover:bg-primary/80">
          Send Message
        </button>
      </div>
    </div>
  );
}
