import * as Icons from "../icons";
import { UserRole } from "@/services/auth.services";

// Role-based navigation configuration
export const ROLE_NAV_DATA: Record<UserRole, any[]> = {
  super_admin: [
    {
      label: "DASHBOARD",
      items: [
        {
          title: "Overview",
          url: "/overview",
          icon: Icons.DashboardIcon,
        },
      ],
    },
    {
      label: "CONTENT MANAGEMENT",
      items: [
        {
          title: "Networks",
          icon: Icons.NetworkIcon,
          items: [
            { title: "Sector Network", url: "/networks/sector" },
            { title: "District Network", url: "/networks/district" },
          ],
        },
        {
          title: "Organizations",
          icon: Icons.OrganizationIcon,
          items: [
            { title: "Local Organization", url: "/organizations/local" },
            {
              title: "International Organization",
              url: "/organizations/international",
            },
            {
              title: "Approve NGO",
              url: "/approvals",
              icon: Icons.PaymentIcon,
            },
          ],
        },
        {
          title: "Area of Focus",
          url: "/focus-areas",
          icon: Icons.FocusIcon,
        },
      ],
    },
    {
      label: "PROJECTS",
      items: [
        {
          title: "All Projects",
          url: "/projects",
          icon: Icons.ProjectIcon,
        },
      ],
    },
    // {
    //   label: "DOCUMENTS",
    //   items: [
    //     {
    //       title: "Certificates",
    //       url: "/documents/certificates",
    //       icon: Icons.CertificateIcon,
    //     },
    //     // {
    //     //   title: "NGO Documents",
    //     //   url: "/documents/ngo",
    //     //   icon: Icons.FolderIcon,
    //     // },
    //   ],
    // },
    {
      label: "USER MANAGEMENT",
      items: [
        {
          title: "Users",
          url: "/users",
          icon: Icons.UsersIcon,
        },
        {
          title: "User Alerts",
          url: "/alerts",
          icon: Icons.AlertIcon,
        },
      ],
    },
    {
      label: "SYSTEM ADMIN",
      items: [
        // {
        //   title: "System Settings",
        //   url: "/admin/settings",
        //   icon: Icons.SettingsIcon,
        // },
        {
          title: "Organization Directory",
          url: "/directory",
          icon: Icons.OrganizationIcon,
        },
        {
          title: "System Logs",
          url: "/admin/logs",
          icon: Icons.DocumentIcon,
        },
        {
          title: "FAQs",
          url: "/admin/FAQ",
          icon: Icons.DocumentIcon,
        },
      ],
    },
  ],
  finance_officer: [
    {
      label: "DASHBOARD",
      items: [
        {
          title: "Overview",
          url: "/overview",
          icon: Icons.DashboardIcon,
        },
      ],
    },
    {
      label: "FINANCIAL MANAGEMENT",
      items: [
        {
          title: "Invoices",
          url: "/financial/invoices",
          icon: Icons.InvoiceIcon,
        },
        {
          title: "Registration Payments",
          url: "/approvals",
          icon: Icons.PaymentIcon,
        },
        {
          title: "Subscription Payments",
          url: "/financial/subscription-payments",
          icon: Icons.PaymentIcon,
        },
        // {
        //   title: "Payments",
        //   url: "/financial/payments",
        //   icon: Icons.PaymentIcon,
        // },
        {
          title: "Receipts",
          url: "/financial/receipts",
          icon: Icons.ReceiptIcon,
        },
        // {
        //   title: "Revenue by Category",
        //   url: "/financial/revenue-category",
        //   icon: Icons.ReportIcon,
        // },
        // {
        //   title: "Pending Uploads",
        //   url: "/financial/pending-uploads",
        //   icon: Icons.DocumentIcon,
        // },
        // {
        //   title: "Refunds & Adjustments",
        //   url: "/financial/refunds",
        //   icon: Icons.PaymentIcon,
        // },
        {
          title: "Financial Reports",
          url: "/financial/reports",
          icon: Icons.ReportIcon,
        },
      ],
    },
    // {
    //   label: "ORGANIZATIONS",
    //   items: [
    //     {
    //       title: "Organization Directory",
    //       url: "/organizations",
    //       icon: Icons.OrganizationIcon,
    //     },
    //     {
    //       title: "Payment Status",
    //       url: "/organizations/payments",
    //       icon: Icons.PaymentIcon,
    //     },
    //   ],
    // },
    {
      label: "PROFILE",
      items: [
        {
          title: "Profile",
          url: "/profile",
          icon: Icons.UserIcon,
        },
      ],
    },
  ],
  ngo_admin: [
    {
      label: "DASHBOARD",
      items: [
        {
          title: "Overview",
          url: "/overview",
          icon: Icons.DashboardIcon,
        },
      ],
    },
    {
      label: "ORGANIZATION",
      items: [
        {
          title: "Create Organization",
          url: "/create-organization",
          icon: Icons.OrganizationIcon,
          showCondition: "no_organization", // Show only if user has no organization
        },
        {
          title: "Organization Profile",
          url: "/organization/profile",
          icon: Icons.OrganizationIcon,
          showCondition: "has_organization", // Show only if user has organization
        },
        {
          title: "Members",
          url: "/organization/members",
          icon: Icons.UsersIcon,
          showCondition: "has_organization", // Show only if user has organization
        },
        {
          title: "Projects",
          url: "/organization/projects",
          icon: Icons.ProjectIcon,
          showCondition: "has_organization", // Show only if user has organization
        },
      ],
    },
    {
      label: "SUBSCRIPTION",
      items: [
        {
          title: "Annual Subscription",
          url: "/subscription/manage",
          icon: Icons.PaymentIcon,
          showCondition: "has_organization", // Show only if user has organization
        },
        {
          title: "Payment Status",
          url: "/subscription/status",
          icon: Icons.CertificateIcon,
          showCondition: "has_organization", // Show only if user has organization
        },
      ],
    },
    {
      label: "NETWORKS",
      items: [
        {
          title: "Sector Network",
          url: "/networks/sector",
          icon: Icons.NetworkIcon,
        },
        {
          title: "District Network",
          url: "/networks/district",
          icon: Icons.NetworkIcon,
        },
      ],
    },
    {
      label: "DOCUMENTS",
      items: [
        {
          title: "Organization Documents",
          url: "/documents/organization",
          icon: Icons.FolderIcon,
        },
        {
          title: "Certificates",
          url: "/documents/certificates",
          icon: Icons.CertificateIcon,
        },
      ],
    },
    {
      label: "PROFILE",
      items: [
        {
          title: "Profile",
          url: "/profile",
          icon: Icons.UserIcon,
        },
      ],
    },
  ],
  staff_registry: [
    {
      label: "DASHBOARD",
      items: [
        {
          title: "Overview",
          url: "/overview",
          icon: Icons.DashboardIcon,
        },
      ],
    },
    {
      label: "REGISTRY MANAGEMENT",
      items: [
        {
          title: "Approve NGO",
          url: "approvals",
          icon: Icons.PaymentIcon,
        },
        {
          title: "Organization Registry",
          url: "/registry/organizations",
          icon: Icons.OrganizationIcon,
        },
        {
          title: "Network Registry",
          url: "/registry/networks",
          icon: Icons.NetworkIcon,
        },
        {
          title: "Document Registry",
          url: "/registry/documents",
          icon: Icons.FolderIcon,
        },
      ],
    },
    {
      label: "USER MANAGEMENT",
      items: [
        {
          title: "Users",
          url: "/users",
          icon: Icons.UsersIcon,
        },
        {
          title: "User Alerts",
          url: "/alerts",
          icon: Icons.AlertIcon,
        },
      ],
    },
    {
      label: "PROFILE",
      items: [
        {
          title: "Profile",
          url: "/profile",
          icon: Icons.UserIcon,
        },
      ],
    },
  ],
  staff_admin: [
    {
      label: "DASHBOARD",
      items: [
        {
          title: "Overview",
          url: "/overview",
          icon: Icons.DashboardIcon,
        },
      ],
    },
    {
      label: "CONTENT MANAGEMENT",
      items: [
        {
          title: "Networks",
          icon: Icons.NetworkIcon,
          items: [
            { title: "Sector Network", url: "/networks/sector" },
            { title: "District Network", url: "/networks/district" },
          ],
        },
        {
          title: "Organizations",
          icon: Icons.OrganizationIcon,
          items: [
            { title: "Organization Directory", url: "/directory" },
            { title: "Local Organization", url: "/organizations/local" },
            {
              title: "International Organization",
              url: "/organizations/international",
            },
            {
              title: "Approve NGO",
              url: "/approvals",
              icon: Icons.PaymentIcon,
            },
          ],
        },
      ],
    },
    {
      label: "USER MANAGEMENT",
      items: [
        {
          title: "Users",
          url: "/users",
          icon: Icons.UsersIcon,
        },
        {
          title: "User Alerts",
          url: "/alerts",
          icon: Icons.AlertIcon,
        },
      ],
    },
    {
      label: "PROFILE",
      items: [
        {
          title: "Profile",
          url: "/profile",
          icon: Icons.UserIcon,
        },
      ],
    },
  ],
  programmes_officer: [
    {
      label: "DASHBOARD",
      items: [
        {
          title: "Overview",
          url: "/overview",
          icon: Icons.DashboardIcon,
        },
      ],
    },
    {
      label: "PROGRAMME MANAGEMENT",
      items: [
        {
          title: "All Projects",
          url: "/projects",
          icon: Icons.ProjectIcon,
        },
        {
          title: "Programme Areas",
          url: "/programmes/areas",
          icon: Icons.FocusIcon,
        },
        {
          title: "Impact Assessment",
          url: "/programmes/impact",
          icon: Icons.ReportIcon,
        },
      ],
    },
    {
      label: "COMPLIANCE",
      items: [
        {
          title: "Compliance Status",
          url: "/compliance/compliance-status",
          icon: Icons.ReportIcon,
        },
        {
          title: "Reviewed This Week",
          url: "/compliance/reviewed-this-week",
          icon: Icons.DocumentIcon,
        },
        {
          title: "Missing Documents",
          url: "/compliance/missing-docs",
          icon: Icons.DocumentIcon,
        },
        {
          title: "Flagged NGOs",
          url: "/compliance/flagged-ngos",
          icon: Icons.AlertIcon,
        },
      ],
    },
    {
      label: "NETWORKS",
      items: [
        {
          title: "Sector Network",
          url: "/networks/sector",
          icon: Icons.NetworkIcon,
        },
        {
          title: "District Network",
          url: "/networks/district",
          icon: Icons.NetworkIcon,
        },
      ],
    },
    {
      label: "PROFILE",
      items: [
        {
          title: "Profile",
          url: "/profile",
          icon: Icons.UserIcon,
        },
      ],
    },
  ],
  cso_chair: [
    {
      label: "DASHBOARD",
      items: [
        {
          title: "Overview",
          url: "/overview",
          icon: Icons.DashboardIcon,
        },
        {
          title: "Messages",
          url: "/overview/cso-chair-messages-page",
          icon: Icons.DashboardIcon,
        },
      ],
    },
    {
      label: "NETWORK LEADERSHIP",
      items: [
        {
          title: "Network Members",
          url: "/network/members",
          icon: Icons.UsersIcon,
        },
        {
          title: "Network Activities",
          url: "/network/activities",
          icon: Icons.ProjectIcon,
        },
        {
          title: "Network Reports",
          url: "/network/reports",
          icon: Icons.ReportIcon,
        },
      ],
    },
    {
      label: "PROFILE",
      items: [
        {
          title: "Profile",
          url: "/profile",
          icon: Icons.UserIcon,
        },
      ],
    },
  ],
};

// Function to get navigation data based on user role
export function getNavDataByRole(userRole: UserRole) {
  return ROLE_NAV_DATA[userRole] || ROLE_NAV_DATA.ngo_admin;
}
