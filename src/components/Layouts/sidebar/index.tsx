"use client";

import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { getNavDataByRole } from "./data/role-based-nav";
import { ArrowLeftIcon, ChevronUp, LogoutIcon } from "./icons";
import { MenuItem } from "./menu-item";
import { useSidebarContext } from "./sidebar-context";
import { useRouter } from "next/navigation";
import { getCurrentUser } from "@/utils/auth.utils";
import { UserRole } from "@/services/auth.services";
import Image from "next/image";
//import { useTheme } from "next-themes";

// TypeScript interfaces for navigation data structure
interface NavItem {
  title: string;
  url?: string;
  icon?: React.ComponentType<{
    className?: string;
    "aria-hidden"?: boolean | string;
  }>;
  items?: NavItem[];
  showCondition?: "has_organization" | "no_organization";
}

interface NavSection {
  label: string;
  items: NavItem[];
}

export function Sidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const { setIsOpen, isOpen, isMobile, toggleSidebar, mounted } =
    useSidebarContext();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [navData, setNavData] = useState<NavSection[]>([]);
  const [user, setUser] = useState<any>(null);
  // const { theme } = useTheme();

  // const logoSrc =
  //   theme === "dark"
  //     ? "/images/logo/mabungwe_2.png"
  //     : "/images/logo/mabungwe.png";

  // Get user role and set navigation data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const currentUser = await getCurrentUser();
        if (currentUser?.role?.name) {
          setUser(currentUser);
          const roleNavData = getNavDataByRole(
            currentUser.role.name as UserRole,
          );

          // Filter navigation items based on user's organization status
          const filteredNavData = roleNavData.map((section: NavSection) => ({
            ...section,
            items: section.items.filter((item: NavItem) => {
              if (!item.showCondition) return true;

              const hasOrganization = currentUser.ngoId || currentUser.ngoId;

              if (item.showCondition === "has_organization") {
                return hasOrganization;
              } else if (item.showCondition === "no_organization") {
                return !hasOrganization;
              }

              return true;
            }),
          }));

          setNavData(filteredNavData);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };

    fetchUserData();
  }, []);

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) => (prev.includes(title) ? [] : [title]));
  };

  useEffect(() => {
    navData?.forEach((section: NavSection) => {
      section?.items?.forEach((item: NavItem) => {
        item?.items?.forEach((subItem: NavItem) => {
          if (
            subItem?.url === pathname &&
            !expandedItems.includes(item.title)
          ) {
            setExpandedItems((prev) => [...prev, item.title]);
          }
        });
      });
    });
  }, [pathname, expandedItems, navData]);

  if (!mounted) {
    return (
      <aside className="hidden h-screen w-[290px] border-r border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-dark lg:block">
        {/* Skeleton loader */}
      </aside>
    );
  }

  return (
    <>
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 transition-opacity duration-300"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}

      <aside
        className={cn(
          "max-w-[290px] overflow-hidden border-r border-gray-200 bg-white transition-[width] duration-200 ease-linear dark:border-gray-800 dark:bg-gray-dark",
          isMobile ? "fixed bottom-0 top-0 z-50" : "sticky top-0 h-screen",
          isOpen ? "w-full" : "w-0",
        )}
        aria-label="Main navigation"
        aria-hidden={!isOpen}
      >
        <div className="flex h-full flex-col py-10 pl-[25px] pr-[7px]">
          <div className="relative pr-4.5">
            {isMobile && (
              <button
                onClick={toggleSidebar}
                className="absolute left-3/4 right-4.5 top-1/2 -translate-y-1/2 text-right"
              >
                <span className="sr-only">Close Menu</span>
                <ArrowLeftIcon className="ml-auto size-7" />
              </button>
            )}
          </div>

          {/* <div className="flex justify-center">
            <Image
              src={logoSrc}
              alt="Congoma logo"
              width={60}
              height={60}
              className="object-contain"
            />
          </div> */}

          <div className="custom-scrollbar mt-6 flex-1 overflow-y-auto pr-3 min-[850px]:mt-10">
            {navData?.map((section) => (
              <div key={section?.label} className="mb-6">
                <h2 className="mb-5 text-sm font-medium text-dark-4 dark:text-dark-6">
                  {section?.label}
                </h2>

                <nav role="navigation" aria-label={section?.label}>
                  <ul className="space-y-2">
                    {section?.items?.map((item: NavItem) => (
                      <li key={item?.title}>
                        {item.title === "Logout" ? (
                          <MenuItem
                            as="button"
                            isActive={false}
                            onClick={() => {
                              if (isMobile) toggleSidebar();
                              router.push("/");
                            }}
                            className="flex items-center gap-3 py-3"
                          >
                            {item.icon && (
                              <item.icon
                                className="size-6 shrink-0"
                                aria-hidden="true"
                              />
                            )}
                            <span>{item.title}</span>
                          </MenuItem>
                        ) : item?.items?.length ? (
                          <div>
                            <MenuItem
                              isActive={item.items.some(
                                ({ url }) => url === pathname,
                              )}
                              onClick={() => toggleExpanded(item.title)}
                            >
                              {item.icon && (
                                <item.icon
                                  className="size-6 shrink-0"
                                  aria-hidden="true"
                                />
                              )}
                              <span>{item.title}</span>
                              <ChevronUp
                                className={cn(
                                  "ml-auto rotate-180 transition-transform duration-200",
                                  expandedItems.includes(item.title) &&
                                    "rotate-0",
                                )}
                                aria-hidden="true"
                              />
                            </MenuItem>

                            {expandedItems.includes(item.title) && (
                              <ul
                                className="ml-9 mr-0 space-y-1.5 pb-[15px] pr-0 pt-2"
                                role="menu"
                              >
                                {item.items.map((subItem) => (
                                  <li key={subItem?.title} role="none">
                                    <MenuItem
                                      as="link"
                                      href={subItem?.url || "#"}
                                      isActive={pathname === subItem?.url}
                                    >
                                      <span>{subItem?.title}</span>
                                    </MenuItem>
                                  </li>
                                ))}
                              </ul>
                            )}
                          </div>
                        ) : (
                          <MenuItem
                            className="flex items-center gap-3 py-3"
                            as="link"
                            href={
                              item?.url ||
                              `/${item?.title?.toLowerCase()?.split(" ")?.join("-")}`
                            }
                            isActive={pathname === item?.url}
                          >
                            {item.icon && (
                              <item.icon
                                className="size-6 shrink-0"
                                aria-hidden="true"
                              />
                            )}
                            <span>{item?.title}</span>
                          </MenuItem>
                        )}
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            ))}

            {/* Logout Section - Always available */}
            <div className="mt-auto border-t border-gray-200 pt-6 dark:border-gray-700">
              <nav role="navigation" aria-label="Account">
                <ul className="space-y-2">
                  <li>
                    <MenuItem
                      as="button"
                      isActive={false}
                      onClick={() => {
                        if (isMobile) toggleSidebar();
                        // Clear user data and redirect to home
                        localStorage.removeItem("user");
                        localStorage.removeItem("accessToken");
                        localStorage.removeItem("refreshToken");
                        router.push("/");
                      }}
                      className="flex items-center gap-3 py-3"
                    >
                      <LogoutIcon className="size-5" />
                      <span>Logout</span>
                    </MenuItem>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}
