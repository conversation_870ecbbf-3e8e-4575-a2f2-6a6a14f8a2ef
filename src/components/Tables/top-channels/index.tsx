"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { compactFormat, standardFormat } from "@/lib/format-number";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { getTopChannels } from "../fetch";
import {
  HiOutlineDocumentText,
  HiOutlineArrowDownTray,
  HiOutlineEye,
} from "react-icons/hi2";
import { useEffect, useState } from "react";

export function TopChannels({ className }: { className?: string }) {
  const [data, setData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const channelsData = await getTopChannels();
        setData(channelsData);
      } catch (error) {
        console.error("Error fetching top channels data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div
        className={cn(
          "grid rounded-[10px] bg-white px-7.5 pb-4 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="mb-4 flex items-center justify-between">
          <div>
            <div className="h-6 w-32 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
            <div className="mt-1 h-4 w-48 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
          </div>
          <div className="h-8 w-24 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
        </div>

        {/* Mobile skeleton */}
        <div className="block space-y-3 lg:hidden">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="size-10 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700"></div>
                  <div>
                    <div className="h-4 w-24 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                    <div className="mt-1 h-3 w-32 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-6 w-16 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                  <div className="h-6 w-6 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                  <div className="h-6 w-6 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Desktop skeleton */}
        <div className="hidden lg:block">
          <div className="h-64 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "grid rounded-[10px] bg-white px-7.5 pb-4 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      <div className="mb-4 flex items-center justify-between">
        <div>
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            NGO Documents
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage and track important NGO documentation
          </p>
        </div>
        {/* <button className="rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
          Upload New
        </button> */}
      </div>

      {/* Mobile Cards View */}
      <div className="block space-y-3 lg:hidden">
        {data.map((channel, i) => (
          <div
            key={channel.name + i}
            className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex size-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                  <HiOutlineDocumentText className="size-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {channel.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {channel.sales} KB • {channel.conversion} days ago
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span
                  className={`rounded-full px-2 py-1 text-xs font-medium ${
                    channel.revenues > 2000
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                      : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                  }`}
                >
                  {channel.revenues > 2000 ? "Approved" : "Pending"}
                </span>
                <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                  <HiOutlineEye className="size-4" />
                </button>
                <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                  <HiOutlineArrowDownTray className="size-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="hidden lg:block">
        <Table>
          <TableHeader>
            <TableRow className="border-none uppercase [&>th]:text-center">
              <TableHead className="min-w-[120px] !text-left">
                Document Type
              </TableHead>
              <TableHead className="!text-right">Status</TableHead>
              <TableHead>Size</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead className="!text-center">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((channel, i) => (
              <TableRow key={channel.name + i} className="border-none">
                <TableCell className="!text-left">
                  <div className="flex items-center gap-3">
                    <div className="flex size-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                      <HiOutlineDocumentText className="size-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <div className="font-medium text-dark dark:text-white">
                        {channel.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {channel.sales} KB
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="!text-right">
                  <span
                    className={`rounded-full px-2 py-1 text-xs font-medium ${
                      channel.revenues > 2000
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                        : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                    }`}
                  >
                    {channel.revenues > 2000 ? "Approved" : "Pending"}
                  </span>
                </TableCell>
                <TableCell className="text-center">
                  {standardFormat(channel.sales)} KB
                </TableCell>
                <TableCell className="text-center">
                  {channel.conversion} days ago
                </TableCell>
                <TableCell className="!text-center">
                  <div className="flex items-center justify-center gap-2">
                    <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                      <HiOutlineEye className="size-4" />
                    </button>
                    <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                      <HiOutlineArrowDownTray className="size-4" />
                    </button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
