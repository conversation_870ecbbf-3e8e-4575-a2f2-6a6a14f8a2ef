"use client";

import React from "react";
import { Download, FileText, Database, X } from "lucide-react";
import { ExportModalProps } from "@/utils/exportUtils";

export default function ExportModal({
  isOpen,
  onClose,
  onExport,
  title = "Export Data",
  description = "Choose the format to export your data",
}: ExportModalProps) {
  if (!isOpen) return null;

  const handleExport = (format: 'csv' | 'json') => {
    onExport(format);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="mx-4 w-full max-w-md rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <p className="mb-6 text-sm text-gray-600 dark:text-gray-400">
          {description}
        </p>

        <div className="space-y-3">
          <button
            onClick={() => handleExport('csv')}
            className="flex w-full items-center gap-3 rounded-lg border border-gray-200 bg-white p-4 text-left transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
              <FileText className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white">
                CSV Format
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Compatible with Excel and other spreadsheet applications
              </p>
            </div>
          </button>

          <button
            onClick={() => handleExport('json')}
            className="flex w-full items-center gap-3 rounded-lg border border-gray-200 bg-white p-4 text-left transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
              <Database className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white">
                JSON Format
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Structured data format for developers and APIs
              </p>
            </div>
          </button>
        </div>

        <div className="mt-6 flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}
